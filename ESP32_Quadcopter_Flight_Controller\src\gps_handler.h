/*
 * GPS Handler for ESP32 Quadcopter Flight Controller
 * 
 * This module handles NEO-6M GPS module communication and processing
 */

#ifndef GPS_HANDLER_H
#define GPS_HANDLER_H

#include "config.h"
#include <TinyGPS++.h>

// GPS objects
extern TinyGPSPlus gps;
extern HardwareSerial SerialGPS;

// GPS data
extern GPSData gpsData;

// Home position for return-to-launch
extern double homeLatitude;
extern double homeLongitude;
extern float homeAltitude;
extern bool homePositionSet;

// Function declarations
bool initializeGPS();
void updateGPS();
void processGPSData();
bool isGPSFixValid();
void setHomePosition();
void clearHomePosition();
float calculateDistanceToHome();
float calculateBearingToHome();
float calculateDistance(double lat1, double lon1, double lat2, double lon2);
float calculateBearing(double lat1, double lon1, double lat2, double lon2);

// GPS status functions
void printGPSStatus();
bool hasGoodGPSFix();
void waitForGPSFix();

// Utility functions
float degreesToRadians(float degrees);
float radiansToDegrees(float radians);

#endif // GPS_HANDLER_H
