/*
 * Sensor management implementation
 */

#include "sensors.h"

// Sensor objects
Adafruit_MPU6050 mpu;
Adafruit_HMC5883_Unified mag = Adafruit_HMC5883_Unified(12345);
MS5611 ms5611;

// Global sensor data
SensorData sensorData;

// Previous values for filtering
float prevRoll = 0, prevPitch = 0, prevYaw = 0;

bool initializeSensors() {
  Serial.println("Initializing MPU6050...");
  if (!mpu.begin()) {
    Serial.println("Failed to find MPU6050 chip");
    return false;
  }
  
  // Configure MPU6050
  mpu.setAccelerometerRange(MPU6050_RANGE_2_G);
  mpu.setGyroRange(MPU6050_RANGE_250_DEG);
  mpu.setFilterBandwidth(MPU6050_BAND_21_HZ);
  
  Serial.println("Initializing HMC5883L...");
  if (!mag.begin()) {
    Serial.println("Failed to find HMC5883L chip");
    return false;
  }
  Serial.println("HMC5883L magnetometer initialized successfully!");
  
  Serial.println("Initializing MS5611...");
  if (!ms5611.begin()) {
    Serial.println("Failed to find MS5611 chip");
    return false;
  }
  
  // Initialize sensor offsets to zero
  resetSensorOffsets();
  
  // Take initial ground pressure reading
  delay(100);
  sensorData.groundPressure = ms5611.getPressure();
  
  Serial.println("All sensors initialized successfully!");
  return true;
}

void updateSensors() {
  readMPU6050();
  readHMC5883L();
  readMS5611();
  
  // Calculate attitude using sensor fusion
  calculateAttitude(GYRO_INTEGRATION_TIME);
}

void readMPU6050() {
  sensors_event_t a, g, temp;
  mpu.getEvent(&a, &g, &temp);
  
  // Apply calibration offsets
  sensorData.accelX = a.acceleration.x - sensorData.accelOffsetX;
  sensorData.accelY = a.acceleration.y - sensorData.accelOffsetY;
  sensorData.accelZ = a.acceleration.z - sensorData.accelOffsetZ;
  
  sensorData.gyroX = g.gyro.x - sensorData.gyroOffsetX;
  sensorData.gyroY = g.gyro.y - sensorData.gyroOffsetY;
  sensorData.gyroZ = g.gyro.z - sensorData.gyroOffsetZ;
  
  sensorData.temperature = temp.temperature;
}

void readHMC5883L() {
  sensors_event_t event;
  mag.getEvent(&event);

  // Apply calibration offsets
  sensorData.magX = event.magnetic.x - sensorData.magOffsetX;
  sensorData.magY = event.magnetic.y - sensorData.magOffsetY;
  sensorData.magZ = event.magnetic.z - sensorData.magOffsetZ;

  // Debug: Print magnetometer values every 2 seconds
  static unsigned long lastMagDebug = 0;
  if (millis() - lastMagDebug > 2000) {
    Serial.print("Magnetometer - Raw: X=");
    Serial.print(event.magnetic.x);
    Serial.print(" Y=");
    Serial.print(event.magnetic.y);
    Serial.print(" Z=");
    Serial.print(event.magnetic.z);
    Serial.print(" | Calibrated: X=");
    Serial.print(sensorData.magX);
    Serial.print(" Y=");
    Serial.print(sensorData.magY);
    Serial.print(" Z=");
    Serial.println(sensorData.magZ);
    lastMagDebug = millis();
  }
}

void readMS5611() {
  sensorData.pressure = ms5611.getPressure();
  sensorData.altitude = calculateAltitude(sensorData.pressure, sensorData.groundPressure);
}

void calculateAttitude(float deltaTime) {
  // Calculate roll and pitch from accelerometer
  float accelRoll = atan2(sensorData.accelY, sensorData.accelZ) * 180.0 / PI;
  float accelPitch = atan2(-sensorData.accelX, sqrt(sensorData.accelY * sensorData.accelY + sensorData.accelZ * sensorData.accelZ)) * 180.0 / PI;
  
  // Integrate gyroscope data
  float gyroRoll = prevRoll + sensorData.gyroX * deltaTime * 180.0 / PI;
  float gyroPitch = prevPitch + sensorData.gyroY * deltaTime * 180.0 / PI;
  float gyroYaw = prevYaw + sensorData.gyroZ * deltaTime * 180.0 / PI;
  
  // Apply complementary filter
  extern SystemState systemState;
  systemState.roll = COMPLEMENTARY_FILTER_ALPHA * gyroRoll + (1.0 - COMPLEMENTARY_FILTER_ALPHA) * accelRoll;
  systemState.pitch = COMPLEMENTARY_FILTER_ALPHA * gyroPitch + (1.0 - COMPLEMENTARY_FILTER_ALPHA) * accelPitch;
  
  // Calculate yaw from magnetometer with tilt compensation
  float magYaw;
  if (sensorData.magX == 0 && sensorData.magY == 0) {
    // No magnetometer data, use gyro integration
    magYaw = prevYaw + sensorData.gyroZ * deltaTime * 180.0 / PI;
  } else {
    // Tilt-compensated compass calculation
    float rollRad = systemState.roll * PI / 180.0;
    float pitchRad = systemState.pitch * PI / 180.0;

    float magXComp = sensorData.magX * cos(pitchRad) + sensorData.magZ * sin(pitchRad);
    float magYComp = sensorData.magX * sin(rollRad) * sin(pitchRad) +
                     sensorData.magY * cos(rollRad) -
                     sensorData.magZ * sin(rollRad) * cos(pitchRad);

    magYaw = atan2(-magYComp, magXComp) * 180.0 / PI;
  }

  systemState.yaw = constrainAngle(magYaw);

  // Debug: Print YAW calculation every 2 seconds
  static unsigned long lastYawDebug = 0;
  if (millis() - lastYawDebug > 2000) {
    Serial.print("YAW: ");
    Serial.print(systemState.yaw);
    Serial.print("° (MagX=");
    Serial.print(sensorData.magX);
    Serial.print(" MagY=");
    Serial.print(sensorData.magY);
    Serial.println(")");
    lastYawDebug = millis();
  }
  
  // Store current values for next iteration
  prevRoll = systemState.roll;
  prevPitch = systemState.pitch;
  prevYaw = systemState.yaw;
  
  // Store rates
  systemState.rollRate = sensorData.gyroX * 180.0 / PI;
  systemState.pitchRate = sensorData.gyroY * 180.0 / PI;
  systemState.yawRate = sensorData.gyroZ * 180.0 / PI;
  
  // Store altitude
  systemState.altitude = sensorData.altitude;
}

bool checkSensorCalibration() {
  // Check if sensors have been calibrated
  // This is a simple check - in practice, you'd load calibration from EEPROM
  return (sensorData.accelOffsetX != 0 || sensorData.accelOffsetY != 0 || 
          sensorData.gyroOffsetX != 0 || sensorData.gyroOffsetY != 0);
}

void calibrateAccelerometer() {
  Serial.println("Calibrating accelerometer... Keep drone level and still!");
  
  long sumAx = 0, sumAy = 0, sumAz = 0;
  int numReadings = 1000;
  
  for (int i = 0; i < numReadings; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    sumAx += a.acceleration.x;
    sumAy += a.acceleration.y;
    sumAz += a.acceleration.z;
    delay(5);
  }
  
  sensorData.accelOffsetX = sumAx / numReadings;
  sensorData.accelOffsetY = sumAy / numReadings;
  sensorData.accelOffsetZ = (sumAz / numReadings) - 9.81; // Subtract 1g
  
  Serial.println("Accelerometer calibration complete!");
}

void calibrateGyroscope() {
  Serial.println("Calibrating gyroscope... Keep drone absolutely still!");
  
  long sumGx = 0, sumGy = 0, sumGz = 0;
  int numReadings = 2000;
  
  for (int i = 0; i < numReadings; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    sumGx += g.gyro.x;
    sumGy += g.gyro.y;
    sumGz += g.gyro.z;
    delay(2);
  }
  
  sensorData.gyroOffsetX = sumGx / numReadings;
  sensorData.gyroOffsetY = sumGy / numReadings;
  sensorData.gyroOffsetZ = sumGz / numReadings;
  
  Serial.println("Gyroscope calibration complete!");
}

void calibrateMagnetometer() {
  Serial.println("Calibrating magnetometer... Rotate drone in figure-8 pattern!");
  
  float magMinX = 0, magMaxX = 0;
  float magMinY = 0, magMaxY = 0;
  float magMinZ = 0, magMaxZ = 0;
  
  unsigned long startTime = millis();
  while (millis() - startTime < 30000) { // 30 seconds
    sensors_event_t event;
    mag.getEvent(&event);
    
    if (event.magnetic.x < magMinX) magMinX = event.magnetic.x;
    if (event.magnetic.x > magMaxX) magMaxX = event.magnetic.x;
    if (event.magnetic.y < magMinY) magMinY = event.magnetic.y;
    if (event.magnetic.y > magMaxY) magMaxY = event.magnetic.y;
    if (event.magnetic.z < magMinZ) magMinZ = event.magnetic.z;
    if (event.magnetic.z > magMaxZ) magMaxZ = event.magnetic.z;
    
    delay(10);
  }
  
  sensorData.magOffsetX = (magMaxX + magMinX) / 2;
  sensorData.magOffsetY = (magMaxY + magMinY) / 2;
  sensorData.magOffsetZ = (magMaxZ + magMinZ) / 2;
  
  Serial.println("Magnetometer calibration complete!");
}

void calibrateBarometer() {
  Serial.println("Calibrating barometer... Taking ground pressure reference!");
  
  float sumPressure = 0;
  int numReadings = 100;
  
  for (int i = 0; i < numReadings; i++) {
    sumPressure += ms5611.getPressure();
    delay(10);
  }
  
  sensorData.groundPressure = sumPressure / numReadings;
  Serial.print("Ground pressure: ");
  Serial.println(sensorData.groundPressure);
}

float calculateAltitude(float pressure, float groundPressure) {
  return 44330.0 * (1.0 - pow(pressure / groundPressure, 1.0 / 5.255));
}

void resetSensorOffsets() {
  sensorData.accelOffsetX = 0;
  sensorData.accelOffsetY = 0;
  sensorData.accelOffsetZ = 0;
  sensorData.gyroOffsetX = 0;
  sensorData.gyroOffsetY = 0;
  sensorData.gyroOffsetZ = 0;
  sensorData.magOffsetX = 0;
  sensorData.magOffsetY = 0;
  sensorData.magOffsetZ = 0;
  sensorData.groundPressure = 1013.25; // Standard atmospheric pressure
}

float constrainAngle(float angle) {
  while (angle > 180.0) angle -= 360.0;
  while (angle < -180.0) angle += 360.0;
  return angle;
}

float lowPassFilter(float input, float previous, float alpha) {
  return alpha * previous + (1.0 - alpha) * input;
}

// mapFloat function is defined in pid_controller.cpp

void testMagnetometer() {
  Serial.println("=== Magnetometer Test ===");

  for (int i = 0; i < 10; i++) {
    sensors_event_t event;
    mag.getEvent(&event);

    Serial.print("Reading ");
    Serial.print(i + 1);
    Serial.print(": X=");
    Serial.print(event.magnetic.x);
    Serial.print(" Y=");
    Serial.print(event.magnetic.y);
    Serial.print(" Z=");
    Serial.print(event.magnetic.z);
    Serial.print(" | Heading=");
    Serial.print(atan2(event.magnetic.y, event.magnetic.x) * 180.0 / PI);
    Serial.println("°");

    delay(500);
  }

  Serial.println("Rotate the drone and check if values change!");
}
