# RC Channel Mapping and PID Control Parameters

This section covers the crucial steps of configuring the RC channel mapping for the FS-TH9X transmitter and defining the PID control parameters for stable flight. Proper RC mapping ensures intuitive control, while well-tuned PID parameters are essential for a responsive and stable quadcopter.

## 1. RC Channel Mapping for the FS-TH9X

RC channel mapping involves assigning the physical sticks and switches on your FS-TH9X transmitter to specific functions on the drone. The goal is to create a control scheme that is both intuitive and provides access to necessary flight modes and auxiliary functions.

### 1.1. Understanding RC Channels and Modes

The FS-TH9X is a 9-channel transmitter, but the included FS-R9B receiver typically has 8 channels. The four primary flight controls are:

*   **Throttle:** Controls the overall power to the motors, making the drone ascend or descend.
*   **Roll (Aileron):** Tilts the drone left or right.
*   **Pitch (Elevator):** Tilts the drone forward or backward.
*   **Yaw (Rudder):** Rotates the drone left or right.

There are different "modes" for how these controls are assigned to the transmitter sticks. The most common is **Mode 2**:

*   **Left Stick:**
    *   **Up/Down:** Throttle
    *   **Left/Right:** Yaw
*   **Right Stick:**
    *   **Up/Down:** Pitch
    *   **Left/Right:** Roll

Your FS-TH9X transmitter can be configured for different modes. Ensure it is set to the mode you are most comfortable with (we will assume Mode 2 for this guide).

### 1.2. Mapping Channels in the ESP32 Code

In the ESP32 flight control code, you will read the PWM signals from each receiver channel and map them to the corresponding control variables. The `pulseIn()` function or interrupt-based methods will give you a pulse width value, typically between 1000µs (microseconds) and 2000µs.

**Procedure:**

1.  **Connect Receiver to ESP32:** Connect the receiver channels to the ESP32 GPIO pins as defined in your wiring diagram.
2.  **Create a Test Sketch:** Write a simple Arduino sketch to read the PWM values from each receiver channel and print them to the Serial Monitor. This will help you verify which channel corresponds to which stick/switch on your transmitter.

    **Example Test Sketch:**

    ```cpp
    #define RC_CH1_PIN 13 // Roll
    #define RC_CH2_PIN 12 // Pitch
    #define RC_CH3_PIN 14 // Throttle
    #define RC_CH4_PIN 27 // Yaw
    #define RC_CH5_PIN 26 // Flight Mode

    void setup() {
      Serial.begin(115200);
      pinMode(RC_CH1_PIN, INPUT);
      pinMode(RC_CH2_PIN, INPUT);
      pinMode(RC_CH3_PIN, INPUT);
      pinMode(RC_CH4_PIN, INPUT);
      pinMode(RC_CH5_PIN, INPUT);
    }

    void loop() {
      int ch1 = pulseIn(RC_CH1_PIN, HIGH, 25000);
      int ch2 = pulseIn(RC_CH2_PIN, HIGH, 25000);
      int ch3 = pulseIn(RC_CH3_PIN, HIGH, 25000);
      int ch4 = pulseIn(RC_CH4_PIN, HIGH, 25000);
      int ch5 = pulseIn(RC_CH5_PIN, HIGH, 25000);

      Serial.print("Ch1 (Roll): "); Serial.print(ch1);
      Serial.print(" | Ch2 (Pitch): "); Serial.print(ch2);
      Serial.print(" | Ch3 (Throttle): "); Serial.print(ch3);
      Serial.print(" | Ch4 (Yaw): "); Serial.print(ch4);
      Serial.print(" | Ch5 (Mode): "); Serial.println(ch5);

      delay(100);
    }
    ```

3.  **Map Channels:** Move each stick and switch on your transmitter one at a time and observe which channel's value changes in the Serial Monitor. Record the channel number for each control.

4.  **Map Auxiliary Channels:** Use the remaining channels (e.g., Channel 5, 6) for auxiliary functions like:
    *   **Flight Mode Switching:** Assign a 3-position switch to a channel to switch between different flight modes (e.g., Acro/Rate Mode, Angle/Stabilize Mode, Position Hold Mode).
    *   **Arming/Disarming:** Use a switch to arm and disarm the motors for safety.
    *   **Buzzer/LEDs:** Trigger a buzzer or LEDs for notifications.

### 1.3. Normalizing RC Input

Once you have the raw PWM values (1000-2000µs), you need to normalize them into a range that is useful for the PID controllers. For example, you can map the roll, pitch, and yaw inputs to a desired angle range (e.g., -30 to +30 degrees) or a rate of rotation range (e.g., -200 to +200 degrees/second).

**Example Code Snippet (Conceptual):**

```cpp
// In your main loop or RC input processing function
void processRCInputs() {
  // Assuming rcRoll, rcPitch, rcYaw are raw PWM values (1000-2000)

  // Normalize to a range of -1.0 to 1.0
  float normalizedRoll = (rcRoll - 1500.0) / 500.0;
  float normalizedPitch = (rcPitch - 1500.0) / 500.0;
  float normalizedYaw = (rcYaw - 1500.0) / 500.0;

  // Map to desired angle or rate (example for angle control)
  float desiredRollAngle = normalizedRoll * MAX_ANGLE; // e.g., MAX_ANGLE = 30 degrees
  float desiredPitchAngle = normalizedPitch * MAX_ANGLE;
  float desiredYawRate = normalizedYaw * MAX_YAW_RATE; // e.g., MAX_YAW_RATE = 150 deg/s

  // Handle flight mode switching based on rcMode value
  if (rcMode < 1200) {
    flightMode = ACRO_MODE;
  } else if (rcMode < 1700) {
    flightMode = STABILIZE_MODE;
  } else {
    flightMode = POS_HOLD_MODE;
  }
}
```

## 2. PID Control Parameters for Stable Flight

PID (Proportional-Integral-Derivative) control is the heart of your drone's stability system. Tuning the PID parameters (`Kp`, `Ki`, `Kd`) is a critical and often iterative process. The goal is to find a balance between responsiveness and stability, without oscillations or sluggishness.

### 2.1. Understanding PID Components

*   **P (Proportional) Gain (`Kp`):** This is the primary gain that determines how strongly the drone responds to errors. A higher `Kp` will make the drone more responsive, but too high a value will cause oscillations.
*   **I (Integral) Gain (`Ki`):** This gain helps eliminate steady-state errors. It accumulates past errors and applies a correction to overcome persistent small drifts. A high `Ki` can lead to overshoot and slow oscillations.
*   **D (Derivative) Gain (`Kd`):** This gain acts as a damper, reducing oscillations and improving the drone's ability to hold its attitude. It responds to the rate of change of the error. A high `Kd` can amplify noise and cause high-frequency vibrations.

### 2.2. PID Tuning Procedure

PID tuning is best done in a safe, open area with the drone tethered or at a very low altitude. Always start with low PID values and gradually increase them.

**Recommended Tuning Order:**

1.  **Tune Roll and Pitch P-gains (`Kp_roll`, `Kp_pitch`):**
    *   Start with `Ki` and `Kd` set to zero for all axes.
    *   Begin with a low `Kp` value for roll and pitch (e.g., `Kp_roll = 1.0`, `Kp_pitch = 1.0`).
    *   Gradually increase `Kp` for roll and pitch. The drone should become more stable and responsive.
    *   Continue increasing `Kp` until you observe high-frequency oscillations. Then, reduce `Kp` by about 20-30% from that point.

2.  **Tune Roll and Pitch D-gains (`Kd_roll`, `Kd_pitch`):**
    *   With `Kp` set, start increasing `Kd` for roll and pitch.
    *   A higher `Kd` should help dampen the oscillations and make the drone feel more "locked in."
    *   If you increase `Kd` too much, you may see high-frequency vibrations or hear a high-pitched noise from the motors. This is due to the D-term amplifying sensor noise. If this happens, reduce `Kd`.

3.  **Tune Roll and Pitch I-gains (`Ki_roll`, `Ki_pitch`):**
    *   With `Kp` and `Kd` set, start increasing `Ki` for roll and pitch.
    *   The I-term helps the drone hold its angle against external disturbances (like wind). A good `Ki` value will make the drone feel more stable and less prone to drifting.
    *   If `Ki` is too high, you will see slow, large oscillations (wobbles).

4.  **Tune Yaw P, I, and D gains (`Kp_yaw`, `Ki_yaw`, `Kd_yaw`):**
    *   The yaw axis is typically less critical than roll and pitch, and it often requires different PID values.
    *   Follow a similar procedure for tuning yaw: start with `Kp_yaw`, then `Kd_yaw`, and finally `Ki_yaw`.
    *   The yaw axis is often less prone to oscillation, so you may be able to use a higher `Kp_yaw`.

### 2.3. Initial PID Control Parameters (Starting Point)

PID values are highly dependent on the drone's frame, motors, propellers, weight, and other factors. The following are **example starting points** and will almost certainly need to be adjusted for your specific build.

**Example Initial PID Values:**

| Parameter      | Roll          | Pitch         | Yaw           |
| :------------- | :------------ | :------------ | :------------ |
| **Kp**         | 1.2           | 1.2           | 2.0           |
| **Ki**         | 0.02          | 0.02          | 0.1           |
| **Kd**         | 0.5           | 0.5           | 0.2           |

**PID Implementation in Code (Conceptual):**

```cpp
// PID constants (to be tuned)
float Kp_roll = 1.2, Ki_roll = 0.02, Kd_roll = 0.5;
float Kp_pitch = 1.2, Ki_pitch = 0.02, Kd_pitch = 0.5;
float Kp_yaw = 2.0, Ki_yaw = 0.1, Kd_yaw = 0.2;

// PID variables
float error_roll, prev_error_roll, integral_roll, derivative_roll, pid_roll_output;
float error_pitch, prev_error_pitch, integral_pitch, derivative_pitch, pid_pitch_output;
float error_yaw, prev_error_yaw, integral_yaw, derivative_yaw, pid_yaw_output;

unsigned long last_pid_time = 0;

void calculatePID() {
  unsigned long current_time = micros();
  float dt = (current_time - last_pid_time) / 1000000.0; // Time difference in seconds
  last_pid_time = current_time;

  // --- Roll PID ---
  error_roll = desired_roll_angle - current_roll_angle;
  integral_roll += error_roll * dt;
  derivative_roll = (error_roll - prev_error_roll) / dt;
  pid_roll_output = Kp_roll * error_roll + Ki_roll * integral_roll + Kd_roll * derivative_roll;
  prev_error_roll = error_roll;

  // --- Pitch PID ---
  error_pitch = desired_pitch_angle - current_pitch_angle;
  integral_pitch += error_pitch * dt;
  derivative_pitch = (error_pitch - prev_error_pitch) / dt;
  pid_pitch_output = Kp_pitch * error_pitch + Ki_pitch * integral_pitch + Kd_pitch * derivative_pitch;
  prev_error_pitch = error_pitch;

  // --- Yaw PID ---
  error_yaw = desired_yaw_rate - current_yaw_rate; // Yaw is often controlled by rate
  integral_yaw += error_yaw * dt;
  derivative_yaw = (error_yaw - prev_error_yaw) / dt;
  pid_yaw_output = Kp_yaw * error_yaw + Ki_yaw * integral_yaw + Kd_yaw * derivative_yaw;
  prev_error_yaw = error_yaw;

  // Anti-windup for integral terms (constrain integral to a reasonable range)
  integral_roll = constrain(integral_roll, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_pitch = constrain(integral_pitch, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_yaw = constrain(integral_yaw, -INTE_LIMIT, INTEGRAL_LIMIT);
}
```

### 2.4. PID Tuning Tips

*   **Tune one axis at a time:** It can be helpful to focus on tuning roll first, then pitch, then yaw.
*   **Use a flight mode switch:** Set up a switch on your transmitter to toggle between a known stable set of PID values and the new values you are testing. This allows you to quickly revert if the new values are unstable.
*   **Record your flights:** If possible, record video of your tuning flights to review the drone's behavior and identify oscillations or other issues.
*   **Telemetry:** Use the TFT display or a serial connection to monitor real-time PID outputs and sensor data. This can provide valuable insights during tuning.
*   **Be patient:** PID tuning is an art as much as a science. It takes time and practice to get it right.

By carefully mapping your RC channels and methodically tuning your PID parameters, you will be well on your way to achieving a stable and responsive quadcopter. The next phase will focus on implementing failsafe mechanisms to ensure the safety of your drone and its surroundings.

