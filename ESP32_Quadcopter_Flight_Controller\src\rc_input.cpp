/*
 * RC Input implementation
 */

#include "rc_input.h"

// RC data structure
RCData rcData;

// Interrupt variables for PWM measurement
volatile unsigned long rcThrottleStart = 0, rcRollStart = 0, rcPitchStart = 0;
volatile unsigned long rcYawStart = 0, rcModeStart = 0, rcAux1Start = 0, rcAux2Start = 0;
volatile int rcThrottlePulse = 1500, rcRollPulse = 1500, rcPitchPulse = 1500;
volatile int rcYawPulse = 1500, rcModePulse = 1500, rcAux1Pulse = 1500, rcAux2Pulse = 1500;

bool initializeRCInput() {
  // Configure RC input pins
  pinMode(RC_THROTTLE_PIN, INPUT_PULLUP);
  pinMode(RC_ROLL_PIN, INPUT_PULLUP);
  pinMode(RC_PITCH_PIN, INPUT_PULLUP);
  pinMode(RC_YAW_PIN, INPUT_PULLUP);
  pinMode(RC_MODE_PIN, INPUT_PULLUP);
  pinMode(RC_AUX1_PIN, INPUT_PULLUP);
  pinMode(RC_AUX2_PIN, INPUT_PULLUP);
  
  // Attach interrupts for PWM measurement
  attachRCInterrupts();
  
  // Initialize RC data with safe defaults
  rcData.throttle = RC_MIN_PULSE;
  rcData.roll = RC_MID_PULSE;
  rcData.pitch = RC_MID_PULSE;
  rcData.yaw = RC_MID_PULSE;
  rcData.mode = RC_MIN_PULSE;
  rcData.aux1 = RC_MIN_PULSE;
  rcData.aux2 = RC_MIN_PULSE;
  rcData.signalLost = true;
  rcData.lastUpdate = millis();
  
  Serial.println("RC input initialized");
  return true;
}

void updateRCInputs() {
  // Copy volatile interrupt data to RC structure
  noInterrupts();
  rcData.throttle = constrainRC(rcThrottlePulse);
  rcData.roll = constrainRC(rcRollPulse);
  rcData.pitch = constrainRC(rcPitchPulse);
  rcData.yaw = constrainRC(rcYawPulse);
  rcData.mode = constrainRC(rcModePulse);
  rcData.aux1 = constrainRC(rcAux1Pulse);
  rcData.aux2 = constrainRC(rcAux2Pulse);
  interrupts();
  
  // Check signal validity
  if (isRCSignalValid()) {
    rcData.signalLost = false;
    rcData.lastUpdate = millis();
  } else {
    // Check for signal timeout
    if (millis() - rcData.lastUpdate > RC_TIMEOUT) {
      rcData.signalLost = true;
      processRCFailsafe();
    }
  }
}

void attachRCInterrupts() {
  attachInterrupt(digitalPinToInterrupt(RC_THROTTLE_PIN), rcThrottleISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_ROLL_PIN), rcRollISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_PITCH_PIN), rcPitchISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_YAW_PIN), rcYawISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_MODE_PIN), rcModeISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_AUX1_PIN), rcAux1ISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_AUX2_PIN), rcAux2ISR, CHANGE);
}

FlightMode determineFlightModeFromRC() {
  // Determine flight mode based on mode switch position
  if (rcData.mode < 1200) {
    return ACRO_MODE;
  } else if (rcData.mode < 1400) {
    return STABILIZE_MODE;
  } else if (rcData.mode < 1600) {
    return ALTITUDE_HOLD_MODE;
  } else if (rcData.mode < 1800) {
    return POSITION_HOLD_MODE;
  } else {
    return RETURN_TO_LAUNCH_MODE;
  }
}

bool isRCSignalValid() {
  return (isRCChannelValid(rcData.throttle) &&
          isRCChannelValid(rcData.roll) &&
          isRCChannelValid(rcData.pitch) &&
          isRCChannelValid(rcData.yaw));
}

void processRCFailsafe() {
  // Set safe RC values during failsafe
  rcData.throttle = RC_MIN_PULSE;  // Minimum throttle
  rcData.roll = RC_MID_PULSE;      // Center stick
  rcData.pitch = RC_MID_PULSE;     // Center stick
  rcData.yaw = RC_MID_PULSE;       // Center stick
  rcData.mode = RC_MIN_PULSE;      // Safe mode
  
  Serial.println("RC FAILSAFE ACTIVATED!");
}

// Interrupt Service Routines
void IRAM_ATTR rcThrottleISR() {
  if (digitalRead(RC_THROTTLE_PIN) == HIGH) {
    rcThrottleStart = micros();
  } else {
    if (rcThrottleStart != 0) {
      rcThrottlePulse = micros() - rcThrottleStart;
    }
  }
}

void IRAM_ATTR rcRollISR() {
  if (digitalRead(RC_ROLL_PIN) == HIGH) {
    rcRollStart = micros();
  } else {
    if (rcRollStart != 0) {
      rcRollPulse = micros() - rcRollStart;
    }
  }
}

void IRAM_ATTR rcPitchISR() {
  if (digitalRead(RC_PITCH_PIN) == HIGH) {
    rcPitchStart = micros();
  } else {
    if (rcPitchStart != 0) {
      rcPitchPulse = micros() - rcPitchStart;
    }
  }
}

void IRAM_ATTR rcYawISR() {
  if (digitalRead(RC_YAW_PIN) == HIGH) {
    rcYawStart = micros();
  } else {
    if (rcYawStart != 0) {
      rcYawPulse = micros() - rcYawStart;
    }
  }
}

void IRAM_ATTR rcModeISR() {
  if (digitalRead(RC_MODE_PIN) == HIGH) {
    rcModeStart = micros();
  } else {
    if (rcModeStart != 0) {
      rcModePulse = micros() - rcModeStart;
    }
  }
}

void IRAM_ATTR rcAux1ISR() {
  if (digitalRead(RC_AUX1_PIN) == HIGH) {
    rcAux1Start = micros();
  } else {
    if (rcAux1Start != 0) {
      rcAux1Pulse = micros() - rcAux1Start;
    }
  }
}

void IRAM_ATTR rcAux2ISR() {
  if (digitalRead(RC_AUX2_PIN) == HIGH) {
    rcAux2Start = micros();
  } else {
    if (rcAux2Start != 0) {
      rcAux2Pulse = micros() - rcAux2Start;
    }
  }
}

// Utility functions
int constrainRC(int value) {
  if (value < RC_MIN_PULSE) return RC_MIN_PULSE;
  if (value > RC_MAX_PULSE) return RC_MAX_PULSE;
  return value;
}

bool isRCChannelValid(int pulse) {
  return (pulse >= RC_MIN_PULSE && pulse <= RC_MAX_PULSE);
}

void printRCValues() {
  Serial.print("RC: T=");
  Serial.print(rcData.throttle);
  Serial.print(" R=");
  Serial.print(rcData.roll);
  Serial.print(" P=");
  Serial.print(rcData.pitch);
  Serial.print(" Y=");
  Serial.print(rcData.yaw);
  Serial.print(" M=");
  Serial.print(rcData.mode);
  Serial.print(" Signal=");
  Serial.println(rcData.signalLost ? "LOST" : "OK");
}
