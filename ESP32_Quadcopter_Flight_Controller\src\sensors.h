/*
 * Sensor management for ESP32 Quadcopter Flight Controller
 * 
 * This module handles all sensor operations including:
 * - MPU6050 (accelerometer/gyroscope)
 * - HMC5883L (magnetometer)
 * - MS5611 (barometer)
 * - Sensor fusion and filtering
 */

#ifndef SENSORS_H
#define SENSORS_H

#include "config.h"
#include <Adafruit_MPU6050.h>
#include <Adafruit_HMC5883_U.h>
#include <MS5611.h>
#include <Adafruit_Sensor.h>

// Sensor objects
extern Adafruit_MPU6050 mpu;
extern Adafruit_HMC5883_Unified mag;
extern MS5611 ms5611;

// Sensor data
extern SensorData sensorData;

// Complementary filter parameters
#define COMPLEMENTARY_FILTER_ALPHA 0.98
#define GYRO_INTEGRATION_TIME 0.004  // 250Hz loop rate

// Function declarations
bool initializeSensors();
void updateSensors();
void readMPU6050();
void readHMC5883L();
void readMS5611();
void calculateAttitude(float deltaTime);
void complementaryFilter(float deltaTime);
bool checkSensorCalibration();
void calibrateAccelerometer();
void calibrateGyroscope();
void calibrateMagnetometer();
void calibrateBarometer();
float calculateAltitude(float pressure, float groundPressure);
void resetSensorOffsets();

// Utility functions
float constrainAngle(float angle);
float lowPassFilter(float input, float previous, float alpha);
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);
void testMagnetometer();

#endif // SENSORS_H
