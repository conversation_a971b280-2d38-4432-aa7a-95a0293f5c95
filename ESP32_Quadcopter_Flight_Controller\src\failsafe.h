/*
 * Failsafe system for ESP32 Quadcopter Flight Controller
 * 
 * This module implements safety mechanisms to handle emergency situations
 */

#ifndef FAILSAFE_H
#define FAILSAFE_H

#include "config.h"

// Failsafe types
enum FailsafeType {
  FAILSAFE_NONE = 0,
  FAILSAFE_RC_LOST = 1,
  FAILSAFE_LOW_BATTERY = 2,
  FAILSAFE_GPS_LOST = 3,
  FAILSAFE_SENSOR_ERROR = 4,
  FAILSAFE_MANUAL = 5
};

// Failsafe actions
enum FailsafeAction {
  ACTION_NONE = 0,
  ACTION_DISARM = 1,
  ACTION_LAND = 2,
  ACTION_RETURN_TO_LAUNCH = 3,
  ACTION_HOLD_POSITION = 4
};

// Failsafe state
extern FailsafeType activeFailsafe;
extern FailsafeAction failsafeAction;
extern bool failsafeTriggered;
extern unsigned long failsafeStartTime;

// Function declarations
void initializeFailsafe();
void checkFailsafe();
void triggerFailsafe(FailsafeType type);
void clearFailsafe();
void executeFailsafeAction();

// Individual failsafe checks
bool checkRCFailsafe();
bool checkBatteryFailsafe();
bool checkGPSFailsafe();
bool checkSensorFailsafe();
bool checkGeofenceFailsafe();

// Failsafe actions
void executeDisarm();
void executeLand();
void executeReturnToLaunch();
void executeHoldPosition();

// Utility functions
const char* getFailsafeTypeName(FailsafeType type);
const char* getFailsafeActionName(FailsafeAction action);
void printFailsafeStatus();

#endif // FAILSAFE_H
