/*
 * Failsafe system implementation
 */

#include "failsafe.h"

// Failsafe state
FailsafeType activeFailsafe = FAILSAFE_NONE;
FailsafeAction failsafeAction = ACTION_NONE;
bool failsafeTriggered = false;
unsigned long failsafeStartTime = 0;

void initializeFailsafe() {
  activeFailsafe = FAILSAFE_NONE;
  failsafeAction = ACTION_NONE;
  failsafeTriggered = false;
  failsafeStartTime = 0;
  
  Serial.println("Failsafe system initialized");
}

void checkFailsafe() {
  extern SystemState systemState;
  
  // Don't check failsafe if already active
  if (failsafeTriggered) {
    executeFailsafeAction();
    return;
  }
  
  // Check various failsafe conditions
  if (checkRCFailsafe()) {
    triggerFailsafe(FAILSAFE_RC_LOST);
    return;
  }
  
  if (checkBatteryFailsafe()) {
    triggerFailsafe(FAILSAFE_LOW_BATTERY);
    return;
  }
  
  if (checkSensorFailsafe()) {
    triggerFailsafe(FAILSAFE_SENSOR_ERROR);
    return;
  }
  
  // GPS failsafe only matters in GPS-dependent modes
  extern FlightMode currentFlightMode;
  if (currentFlightMode == POSITION_HOLD_MODE || currentFlightMode == RETURN_TO_LAUNCH_MODE) {
    if (checkGPSFailsafe()) {
      triggerFailsafe(FAILSAFE_GPS_LOST);
      return;
    }
  }
  
  // Update system state
  systemState.failsafeActive = failsafeTriggered;
}

void triggerFailsafe(FailsafeType type) {
  extern SystemState systemState;
  extern FlightMode currentFlightMode;
  
  activeFailsafe = type;
  failsafeTriggered = true;
  failsafeStartTime = millis();
  systemState.failsafeActive = true;
  
  // Determine appropriate action based on failsafe type and current state
  switch (type) {
    case FAILSAFE_RC_LOST:
      if (currentFlightMode == POSITION_HOLD_MODE || currentFlightMode == RETURN_TO_LAUNCH_MODE) {
        failsafeAction = ACTION_RETURN_TO_LAUNCH;
      } else {
        failsafeAction = ACTION_LAND;
      }
      break;
      
    case FAILSAFE_LOW_BATTERY:
      failsafeAction = ACTION_LAND;
      break;
      
    case FAILSAFE_GPS_LOST:
      failsafeAction = ACTION_LAND;
      break;
      
    case FAILSAFE_SENSOR_ERROR:
      failsafeAction = ACTION_DISARM;
      break;
      
    case FAILSAFE_MANUAL:
      failsafeAction = ACTION_DISARM;
      break;
      
    default:
      failsafeAction = ACTION_DISARM;
      break;
  }
  
  Serial.print("FAILSAFE TRIGGERED: ");
  Serial.print(getFailsafeTypeName(type));
  Serial.print(" - Action: ");
  Serial.println(getFailsafeActionName(failsafeAction));
  
  printFailsafeStatus();
}

void clearFailsafe() {
  extern SystemState systemState;
  
  activeFailsafe = FAILSAFE_NONE;
  failsafeAction = ACTION_NONE;
  failsafeTriggered = false;
  failsafeStartTime = 0;
  systemState.failsafeActive = false;
  
  Serial.println("Failsafe cleared");
}

void executeFailsafeAction() {
  switch (failsafeAction) {
    case ACTION_DISARM:
      executeDisarm();
      break;
      
    case ACTION_LAND:
      executeLand();
      break;
      
    case ACTION_RETURN_TO_LAUNCH:
      executeReturnToLaunch();
      break;
      
    case ACTION_HOLD_POSITION:
      executeHoldPosition();
      break;
      
    default:
      executeDisarm();
      break;
  }
}

bool checkRCFailsafe() {
  extern RCData rcData;
  
  // Check if RC signal is lost
  return rcData.signalLost;
}

bool checkBatteryFailsafe() {
  extern SystemState systemState;
  
  // Check if battery voltage is too low
  return (systemState.batteryVoltage < BATTERY_MIN_VOLTAGE);
}

bool checkGPSFailsafe() {
  extern SystemState systemState;
  
  // Check if GPS fix is lost
  return (!systemState.gpsData.fix || 
          systemState.gpsData.satellites < GPS_MIN_SATELLITES ||
          systemState.gpsData.hdop > GPS_MAX_HDOP);
}

bool checkSensorFailsafe() {
  extern SystemState systemState;
  
  // Check for sensor errors (simplified check)
  // In a real implementation, you'd check for sensor communication errors,
  // unreasonable values, etc.
  
  // Check for extreme attitude values that might indicate sensor failure
  if (abs(systemState.roll) > 90.0 || abs(systemState.pitch) > 90.0) {
    return true;
  }
  
  // Check for extreme rate values
  if (abs(systemState.rollRate) > 500.0 || 
      abs(systemState.pitchRate) > 500.0 || 
      abs(systemState.yawRate) > 500.0) {
    return true;
  }
  
  return false;
}

bool checkGeofenceFailsafe() {
  // Placeholder for geofence checking
  // Would check if drone has exceeded predefined boundaries
  return false;
}

void executeDisarm() {
  extern bool motorsArmed;
  
  if (motorsArmed) {
    disarmMotors();
    Serial.println("FAILSAFE: Motors disarmed");
  }
}

void executeLand() {
  extern FlightMode currentFlightMode;
  extern bool motorsArmed;
  
  if (motorsArmed) {
    // Switch to land mode
    currentFlightMode = LAND_MODE;
    
    // Implement gentle descent
    // This is a simplified implementation
    extern RCData rcData;
    rcData.throttle = 1200; // Gentle descent throttle
    
    Serial.println("FAILSAFE: Landing initiated");
  }
}

void executeReturnToLaunch() {
  extern FlightMode currentFlightMode;
  extern bool motorsArmed;
  extern bool homePositionSet;
  
  if (motorsArmed && homePositionSet) {
    // Switch to RTL mode
    currentFlightMode = RETURN_TO_LAUNCH_MODE;
    Serial.println("FAILSAFE: Return to launch initiated");
  } else {
    // Fall back to landing if no home position
    executeLand();
  }
}

void executeHoldPosition() {
  extern FlightMode currentFlightMode;
  extern bool motorsArmed;
  
  if (motorsArmed) {
    // Switch to position hold mode
    currentFlightMode = POSITION_HOLD_MODE;
    Serial.println("FAILSAFE: Position hold initiated");
  }
}

const char* getFailsafeTypeName(FailsafeType type) {
  switch (type) {
    case FAILSAFE_NONE: return "NONE";
    case FAILSAFE_RC_LOST: return "RC_LOST";
    case FAILSAFE_LOW_BATTERY: return "LOW_BATTERY";
    case FAILSAFE_GPS_LOST: return "GPS_LOST";
    case FAILSAFE_SENSOR_ERROR: return "SENSOR_ERROR";
    case FAILSAFE_MANUAL: return "MANUAL";
    default: return "UNKNOWN";
  }
}

const char* getFailsafeActionName(FailsafeAction action) {
  switch (action) {
    case ACTION_NONE: return "NONE";
    case ACTION_DISARM: return "DISARM";
    case ACTION_LAND: return "LAND";
    case ACTION_RETURN_TO_LAUNCH: return "RTL";
    case ACTION_HOLD_POSITION: return "HOLD";
    default: return "UNKNOWN";
  }
}

void printFailsafeStatus() {
  Serial.print("Failsafe Status - Type: ");
  Serial.print(getFailsafeTypeName(activeFailsafe));
  Serial.print(", Action: ");
  Serial.print(getFailsafeActionName(failsafeAction));
  Serial.print(", Active: ");
  Serial.print(failsafeTriggered ? "YES" : "NO");
  if (failsafeTriggered) {
    Serial.print(", Duration: ");
    Serial.print((millis() - failsafeStartTime) / 1000);
    Serial.print("s");
  }
  Serial.println();
}
