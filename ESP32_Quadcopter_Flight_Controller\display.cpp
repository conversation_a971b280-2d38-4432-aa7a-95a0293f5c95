/*
 * Display management implementation
 */

#include "display.h"

// Forward declaration
const char* getFlightModeName(FlightMode mode);

// Display object
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS_PIN, TFT_DC_PIN, TFT_RST_PIN);

// Current display page
DisplayPage currentPage = PAGE_FLIGHT_DATA;

bool initializeDisplay() {
  // Initialize TFT display
  tft.initR(INITR_BLACKTAB);
  tft.setRotation(0);
  tft.fillScreen(COLOR_BLACK);
  
  // Display startup message
  tft.setTextColor(COLOR_WHITE);
  tft.setTextSize(1);
  drawCenterText("ESP32 Quadcopter", 20, COLOR_CYAN);
  drawCenterText("Flight Controller", 35, COLOR_CYAN);
  drawCenterText("v1.0", 50, COLOR_WHITE);
  drawCenterText("Initializing...", 80, COLOR_YELLOW);
  
  delay(2000);
  clearDisplay();
  
  Serial.println("Display initialized");
  return true;
}

void updateDisplay() {
  switch (currentPage) {
    case PAGE_FLIGHT_DATA:
      displayFlightData();
      break;
    case PAGE_GPS_DATA:
      displayGPSData();
      break;
    case PAGE_SYSTEM_STATUS:
      displaySystemStatus();
      break;
    case PAGE_RC_DATA:
      displayRCData();
      break;
  }
}

void clearDisplay() {
  tft.fillScreen(COLOR_BLACK);
}

void displayFlightData() {
  extern SystemState systemState;
  extern FlightMode currentFlightMode;
  extern bool motorsArmed;
  
  clearDisplay();
  
  // Title
  drawCenterText("FLIGHT DATA", 5, COLOR_CYAN);
  
  // Flight mode
  tft.setCursor(5, 20);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Mode: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(getFlightModeName(currentFlightMode));
  
  // Armed status
  tft.setCursor(5, 35);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Armed: ");
  tft.setTextColor(motorsArmed ? COLOR_GREEN : COLOR_RED);
  tft.println(motorsArmed ? "YES" : "NO");
  
  // Attitude
  char buffer[20];
  tft.setCursor(5, 55);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Roll: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.roll, 1);
  tft.print(buffer);
  tft.println("°");
  
  tft.setCursor(5, 70);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Pitch: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.pitch, 1);
  tft.print(buffer);
  tft.println("°");
  
  tft.setCursor(5, 85);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Yaw: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.yaw, 1);
  tft.print(buffer);
  tft.println("°");
  
  // Altitude
  tft.setCursor(5, 105);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Alt: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.altitude, 1);
  tft.print(buffer);
  tft.println("m");
  
  // Battery
  tft.setCursor(5, 125);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Batt: ");
  tft.setTextColor(systemState.batteryVoltage > BATTERY_MIN_VOLTAGE ? COLOR_GREEN : COLOR_RED);
  formatFloat(buffer, systemState.batteryVoltage, 1);
  tft.print(buffer);
  tft.println("V");
  
  // Page indicator
  tft.setCursor(5, 150);
  tft.setTextColor(COLOR_CYAN);
  tft.print("Page 1/4");
}

void displayGPSData() {
  extern SystemState systemState;
  
  clearDisplay();
  
  // Title
  drawCenterText("GPS DATA", 5, COLOR_CYAN);
  
  char buffer[20];
  
  // GPS status
  tft.setCursor(5, 20);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Status: ");
  tft.setTextColor(systemState.gpsData.fix ? COLOR_GREEN : COLOR_RED);
  tft.println(systemState.gpsData.fix ? "FIX" : "NO FIX");
  
  // Satellites
  tft.setCursor(5, 35);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Sats: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(systemState.gpsData.satellites);
  
  // HDOP
  tft.setCursor(5, 50);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("HDOP: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.gpsData.hdop, 1);
  tft.println(buffer);
  
  // Coordinates
  tft.setCursor(5, 70);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Lat: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.gpsData.latitude, 6);
  tft.println(buffer);
  
  tft.setCursor(5, 85);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Lon: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.gpsData.longitude, 6);
  tft.println(buffer);
  
  // GPS altitude
  tft.setCursor(5, 105);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("GPS Alt: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.gpsData.altitude, 1);
  tft.print(buffer);
  tft.println("m");
  
  // Speed
  tft.setCursor(5, 120);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Speed: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.gpsData.speed, 1);
  tft.print(buffer);
  tft.println("m/s");
  
  // Page indicator
  tft.setCursor(5, 150);
  tft.setTextColor(COLOR_CYAN);
  tft.print("Page 2/4");
}

void displaySystemStatus() {
  extern SystemState systemState;
  
  clearDisplay();
  
  // Title
  drawCenterText("SYSTEM STATUS", 5, COLOR_CYAN);
  
  char buffer[20];
  
  // Uptime
  tft.setCursor(5, 20);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Uptime: ");
  tft.setTextColor(COLOR_WHITE);
  formatTime(buffer, millis());
  tft.println(buffer);
  
  // CPU load
  tft.setCursor(5, 35);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("CPU: ");
  tft.setTextColor(COLOR_WHITE);
  formatFloat(buffer, systemState.cpuLoad, 1);
  tft.print(buffer);
  tft.println("%");
  
  // Calibration status
  tft.setCursor(5, 55);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Calibrated: ");
  tft.setTextColor(systemState.calibrated ? COLOR_GREEN : COLOR_RED);
  tft.println(systemState.calibrated ? "YES" : "NO");
  
  // Failsafe status
  tft.setCursor(5, 70);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Failsafe: ");
  tft.setTextColor(systemState.failsafeActive ? COLOR_RED : COLOR_GREEN);
  tft.println(systemState.failsafeActive ? "ACTIVE" : "OK");
  
  // Memory usage (placeholder)
  tft.setCursor(5, 90);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Memory: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println("OK");
  
  // Loop rate (placeholder)
  tft.setCursor(5, 105);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Loop: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println("250Hz");
  
  // Page indicator
  tft.setCursor(5, 150);
  tft.setTextColor(COLOR_CYAN);
  tft.print("Page 3/4");
}

void displayRCData() {
  extern RCData rcData;
  
  clearDisplay();
  
  // Title
  drawCenterText("RC DATA", 5, COLOR_CYAN);
  
  // RC signal status
  tft.setCursor(5, 20);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Signal: ");
  tft.setTextColor(rcData.signalLost ? COLOR_RED : COLOR_GREEN);
  tft.println(rcData.signalLost ? "LOST" : "OK");
  
  // RC channels
  tft.setCursor(5, 40);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Throttle: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(rcData.throttle);
  
  tft.setCursor(5, 55);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Roll: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(rcData.roll);
  
  tft.setCursor(5, 70);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Pitch: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(rcData.pitch);
  
  tft.setCursor(5, 85);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Yaw: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(rcData.yaw);
  
  tft.setCursor(5, 100);
  tft.setTextColor(COLOR_YELLOW);
  tft.print("Mode: ");
  tft.setTextColor(COLOR_WHITE);
  tft.println(rcData.mode);
  
  // Page indicator
  tft.setCursor(5, 150);
  tft.setTextColor(COLOR_CYAN);
  tft.print("Page 4/4");
}

void drawCenterText(const char* text, int y, uint16_t color) {
  int16_t x1, y1;
  uint16_t w, h;
  tft.getTextBounds(text, 0, 0, &x1, &y1, &w, &h);
  int x = (DISPLAY_WIDTH - w) / 2;
  tft.setCursor(x, y);
  tft.setTextColor(color);
  tft.println(text);
}

void formatFloat(char* buffer, float value, int decimals) {
  dtostrf(value, 0, decimals, buffer);
}

void formatTime(char* buffer, unsigned long milliseconds) {
  unsigned long seconds = milliseconds / 1000;
  unsigned long minutes = seconds / 60;
  unsigned long hours = minutes / 60;
  
  seconds %= 60;
  minutes %= 60;
  
  sprintf(buffer, "%02lu:%02lu:%02lu", hours, minutes, seconds);
}
