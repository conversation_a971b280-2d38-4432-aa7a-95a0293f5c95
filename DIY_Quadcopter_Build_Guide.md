# DIY Quadcopter Build Guide

This guide provides comprehensive instructions for designing, building, and programming a DIY quadcopter drone using the specified components. It covers hardware connections, flight control software, sensor calibration, GPS integration, RC configuration, PID tuning, failsafe mechanisms, and initial setup and testing procedures.






## 1. Component Analysis





## 2. Flight Control Code Structure





## 2. Flight Control Code Structure



# Flight Control Code Structure for ESP32 Quadcopter

This section outlines the basic software architecture and code structure for the ESP32-based quadcopter. The flight control firmware will be developed using the Arduino IDE environment, leveraging its libraries for ESP32 and various sensors. The code will be modular, with distinct sections for sensor reading, data processing, PID control, motor output, RC input handling, and communication with the GPS and TFT display.

## 1. Development Environment Setup

Before diving into the code, ensure your Arduino IDE is set up for ESP32 development:

1.  **Install ESP32 Board Package:** In Arduino IDE, go to `File > Preferences`, and add `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json` to the 

Additional Boards Manager URLs.` Then, go to `Tools > Board > Boards Manager`, search for "ESP32", and install the "esp32 by Espressif Systems" package.
2.  **Install Libraries:** You will need several libraries for the sensors and display:
    *   **MPU6050/GY-86:** Look for libraries like `Adafruit MPU6050` or `MPU6050_DMP6` (for DMP functionality). You might also need `Adafruit Unified Sensor`.
    *   **HMC5883L:** `Adafruit HMC5883L`.
    *   **MS5611:** `Adafruit MS5611`.
    *   **NEO-6M GPS:** `TinyGPS++` is a popular and robust library for parsing NMEA sentences from GPS modules.
    *   **1.8 TFT SPI:** `Adafruit-ST7735-Library` and `Adafruit-GFX-Library`.
    *   **RC Receiver:** No specific library is typically needed, as you will read PWM signals directly using ESP32's `pulseIn()` function or interrupts.

Install these libraries via `Sketch > Include Library > Manage Libraries...` in the Arduino IDE.

## 2. Overall Code Structure (main.ino)

The main Arduino sketch (`.ino` file) will orchestrate the various functions of the drone. A typical structure includes setup, loop, and various helper functions.

```cpp
// Include necessary libraries
#include <Wire.h> // For I2C communication (GY-86)
#include <SPI.h>  // For SPI communication (TFT Display)
#include <Adafruit_GFX.h> // Core graphics library
#include <Adafruit_ST7735.h> // Hardware-specific library for ST7735
#include <Adafruit_MPU6050.h> // For MPU6050
#include <Adafruit_HMC5883_U.h> // For HMC5883L
#include <Adafruit_MS5611.h> // For MS5611
#include <TinyGPS++.h> // For NEO-6M GPS
#include <HardwareSerial.h> // For ESP32 UART (GPS)

// Define pin assignments (refer to your wiring diagram)
// GY-86 I2C
#define I2C_SDA_PIN 21
#define I2C_SCL_PIN 22

// NEO-6M GPS UART2
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17
HardwareSerial SerialGPS(2); // Use UART2

// 1.8 TFT SPI Display
#define TFT_CS   5
#define TFT_DC   2
#define TFT_RST  4
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS, TFT_DC, TFT_RST);

// RC Receiver PWM Input Pins
#define RC_THROTTLE_PIN 14
#define RC_ROLL_PIN     13
#define RC_PITCH_PIN    12
#define RC_YAW_PIN      27
#define RC_MODE_PIN     26 // Flight mode switch

// ESC PWM Output Pins
#define MOTOR_FR_PIN 32 // Front Right
#define MOTOR_RL_PIN 33 // Rear Left
#define MOTOR_FL_PIN 25 // Front Left
#define MOTOR_RR_PIN 26 // Rear Right

// Global variables for sensor data, control values, etc.
// IMU data
float roll, pitch, yaw; // Filtered angles
float accX, accY, accZ; // Accelerometer raw data
float gyroX, gyroY, gyroZ; // Gyroscope raw data
float magX, magY, magZ; // Magnetometer raw data
float temperature, pressure, altitude; // Barometer data

// GPS data
TinyGPSPlus gps;
double latitude, longitude;
float gpsAltitude;
unsigned long chars;
unsigned short sentences, failedChecksum;

// RC Input values (PWM pulse widths)
volatile int rcThrottle, rcRoll, rcPitch, rcYaw, rcMode;

// PID variables
float pidRollOutput, pidPitchOutput, pidYawOutput;

// Motor output values (PWM duty cycles)
int motorFR, motorRL, motorFL, motorRR;

// Function prototypes
void setupSensors();
void readIMU();
void readBarometer();
void readGPS();
void readRCInputs();
void calculatePID();
void updateMotors();
void updateTFTDisplay();
void attachRCInterrupts();

// Interrupt Service Routines (ISRs) for RC input
void IRAM_ATTR rcThrottleISR();
void IRAM_ATTR rcRollISR();
void IRAM_ATTR rcPitchISR();
void IRAM_ATTR rcYawISR();
void IRAM_ATTR rcModeISR();

void setup() {
  Serial.begin(115200);
  SerialGPS.begin(9600, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);

  // Initialize I2C bus
  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);

  setupSensors();
  attachRCInterrupts();

  // Initialize TFT display
  tft.initR(INITR_BLACKTAB); // Initialize ST7735S chip, black tab
  tft.fillScreen(ST77XX_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextWrap(true);
  tft.print("Drone Init...");

  // Setup PWM for motors
  // ESP32 has 16 PWM channels, 0-15
  // Configure PWM frequency and resolution
  ledcSetup(0, 500, 8); // Channel 0 for Motor FR, 500 Hz, 8-bit resolution
  ledcAttachPin(MOTOR_FR_PIN, 0);
  ledcSetup(1, 500, 8); // Channel 1 for Motor RL
  ledcAttachPin(MOTOR_RL_PIN, 1);
  ledcSetup(2, 500, 8); // Channel 2 for Motor FL
  ledcAttachPin(MOTOR_FL_PIN, 2);
  ledcSetup(3, 500, 8); // Channel 3 for Motor RR
  ledcAttachPin(MOTOR_RR_PIN, 3);

  // Initial motor disarm state (e.g., send minimum PWM signal)
  ledcWrite(0, 0); // All motors off
  ledcWrite(1, 0);
  ledcWrite(2, 0);
  ledcWrite(3, 0);

  Serial.println("Setup Complete");
}

void loop() {
  // Main flight control loop
  readIMU();
  readBarometer();
  readGPS(); // Call frequently to process incoming GPS data
  // readRCInputs(); // Handled by interrupts

  // Sensor fusion (e.g., Complementary Filter or Kalman Filter)
  // This will combine accelerometer, gyroscope, and magnetometer data
  // to get accurate roll, pitch, and yaw angles.
  // For simplicity, we'll assume roll, pitch, yaw are updated by IMU reading function for now.

  calculatePID();
  updateMotors();
  updateTFTDisplay();

  // Add a small delay or use a timer to control loop frequency
  // delay(10); // Example, adjust as needed for desired loop rate
}

// --- Function Implementations ---

void setupSensors() {
  // Initialize MPU6050
  // ... (MPU6050 initialization code)

  // Initialize HMC5883L
  // ... (HMC5883L initialization code)

  // Initialize MS5611
  // ... (MS5611 initialization code)

  Serial.println("Sensors Initialized");
}

void readIMU() {
  // Read raw accelerometer, gyroscope, and magnetometer data
  // Apply calibration offsets and scale factors
  // Implement sensor fusion algorithm (e.g., Complementary Filter or Kalman Filter)
  // to calculate stable roll, pitch, and yaw angles.
  // For example:
  // sensors_event_t a, g, mag;
  // mpu.getEvent(&a, &g, &temp);
  // mag.getEvent(&mag);
  // accX = a.acceleration.x; accY = a.acceleration.y; accZ = a.acceleration.z;
  // gyroX = g.gyro.x; gyroY = g.gyro.y; gyroZ = g.gyro.z;
  // magX = mag.magnetic.x; magY = mag.magnetic.y; magZ = mag.magnetic.z;

  // Update roll, pitch, yaw based on sensor fusion
  // roll = ...; pitch = ...; yaw = ...;
}

void readBarometer() {
  // Read pressure and temperature from MS5611
  // Calculate altitude based on pressure
  // For example:
  // pressure = ms5611.readPressure();
  // temperature = ms5611.readTemperature();
  // altitude = ms5611.readAltitude();
}

void readGPS() {
  // While there is data available from the GPS module, process it
  while (SerialGPS.available() > 0) {
    if (gps.encode(SerialGPS.read())) {
      // A new NMEA sentence has been successfully processed
      if (gps.location.isValid()) {
        latitude = gps.location.lat();
        longitude = gps.location.lng();
      }
      if (gps.altitude.isValid()) {
        gpsAltitude = gps.altitude.meters();
      }
      // You can also check for speed, satellites, etc.
    }
  }
  // Optional: Check for new data periodically if not using interrupts
  // if (millis() > lastGpsTime + 1000) { // Update GPS data every 1 second
  //   lastGpsTime = millis();
  //   Serial.print("Satellites: "); Serial.println(gps.satellites.value());
  //   Serial.print("Lat: "); Serial.println(latitude, 6);
  //   Serial.print("Long: "); Serial.println(longitude, 6);
  //   Serial.print("Alt: "); Serial.println(gpsAltitude);
  // }
}

void IRAM_ATTR rcThrottleISR() {
  // Read PWM pulse width for throttle channel
  rcThrottle = pulseIn(RC_THROTTLE_PIN, HIGH, 25000); // Timeout 25ms
}

void IRAM_ATTR rcRollISR() {
  // Read PWM pulse width for roll channel
  rcRoll = pulseIn(RC_ROLL_PIN, HIGH, 25000);
}

void IRAM_ATTR rcPitchISR() {
  // Read PWM pulse width for pitch channel
  rcPitch = pulseIn(RC_PITCH_PIN, HIGH, 25000);
}

void IRAM_ATTR rcYawISR() {
  // Read PWM pulse width for yaw channel
  rcYaw = pulseIn(RC_YAW_PIN, HIGH, 25000);
}

void IRAM_ATTR rcModeISR() {
  // Read PWM pulse width for flight mode channel
  rcMode = pulseIn(RC_MODE_PIN, HIGH, 25000);
}

void attachRCInterrupts() {
  // Attach interrupts to RC input pins
  // RISING and FALLING edges can be used to measure pulse width more accurately
  // For simplicity, using CHANGE and pulseIn() inside ISR for now.
  // Note: pulseIn() inside ISR can be problematic for very fast loops or other interrupts.
  // A better approach is to use RISING and FALLING edge interrupts to record timestamps
  // and calculate pulse width outside the ISR.
  attachInterrupt(digitalPinToInterrupt(RC_THROTTLE_PIN), rcThrottleISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_ROLL_PIN), rcRollISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_PITCH_PIN), rcPitchISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_YAW_PIN), rcYawISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_MODE_PIN), rcModeISR, CHANGE);
}

void calculatePID() {
  // Implement PID control loops for Roll, Pitch, and Yaw
  // Input: Current roll, pitch, yaw (from sensor fusion) and desired roll, pitch, yaw (from RC input)
  // Output: PID corrections for each axis

  // Example (simplified):
  // float targetRoll = map(rcRoll, 1000, 2000, -30, 30); // Map RC input to desired angle
  // pidRollOutput = Kp_roll * (targetRoll - roll) + Ki_roll * integral_roll + Kd_roll * derivative_roll;

  // Similar for pitch and yaw
}

void updateMotors() {
  // Map PID outputs and throttle to individual motor speeds
  // Ensure motor values are within safe limits (e.g., 1000-2000 for ESCs)
  // Implement motor mixing algorithm for quadcopter (X-configuration or + configuration)

  // Example (simplified for X-configuration):
  // motorFR = rcThrottle + pidPitchOutput - pidRollOutput - pidYawOutput;
  // motorRL = rcThrottle - pidPitchOutput - pidRollOutput + pidYawOutput;
  // motorFL = rcThrottle + pidPitchOutput + pidRollOutput + pidYawOutput;
  // motorRR = rcThrottle - pidPitchOutput + pidRollOutput - pidYawOutput;

  // Constrain motor values to valid PWM range (e.g., 0-255 for 8-bit resolution)
  // ledcWrite(0, motorFR);
  // ledcWrite(1, motorRL);
  // ledcWrite(2, motorFL);
  // ledcWrite(3, motorRR);
}

void updateTFTDisplay() {
  // Display real-time flight data on the 1.8 TFT screen
  // Examples: Roll, Pitch, Yaw, Altitude, GPS coordinates, Battery Voltage, Flight Mode
  // tft.setCursor(0, 0);
  // tft.setTextColor(ST77XX_WHITE, ST77XX_BLACK);
  // tft.print("Roll: "); tft.println(roll);
  // tft.print("Pitch: "); tft.println(pitch);
  // tft.print("Yaw: "); tft.println(yaw);
  // tft.print("Alt: "); tft.println(altitude);
  // tft.print("Lat: "); tft.println(latitude, 4);
  // tft.print("Lon: "); tft.println(longitude, 4);
}
```

## 3. Key Code Modules and Their Responsibilities

### 3.1. Sensor Data Acquisition (GY-86: MPU6050, HMC5883L, MS5611)

This module is responsible for reading raw data from the IMU and barometer. It will involve:

*   **MPU6050:** Reading accelerometer and gyroscope values. The MPU6050 has a Digital Motion Processor (DMP) that can perform on-chip sensor fusion, providing more stable and accurate attitude data (quaternions or Euler angles). Using the DMP is highly recommended to offload processing from the ESP32.
*   **HMC5883L:** Reading magnetometer values. These values will be used to correct yaw drift and provide an absolute heading.
*   **MS5611:** Reading pressure and temperature. Altitude will be calculated from pressure readings.

**Implementation Details:**
*   Use the `Wire.h` library for I2C communication.
*   Initialize each sensor and configure its settings (e.g., data rates, ranges).
*   Implement functions to read raw data and apply any necessary calibration offsets or scale factors.

### 3.2. Sensor Fusion

Raw sensor data from the accelerometer, gyroscope, and magnetometer needs to be combined and filtered to produce accurate and stable orientation estimates (roll, pitch, yaw). Common algorithms include:

*   **Complementary Filter:** A simple and effective filter that combines gyroscope data (good for short-term accuracy) with accelerometer/magnetometer data (good for long-term stability).
*   **Kalman Filter:** A more advanced and computationally intensive filter that provides optimal estimates by considering noise characteristics of each sensor. While more complex, it can yield superior results.

**Implementation Details:**
*   This will be a critical part of the `readIMU()` function or a separate `calculateAttitude()` function.
*   The output of this module will be the `roll`, `pitch`, and `yaw` variables used by the PID controller.

### 3.3. RC Input Processing (FS-TH9X Receiver)

This module handles reading the PWM signals from the RC receiver and converting them into usable control values (e.g., 1000-2000 microsecond pulse widths). Using interrupts for each RC channel is the most efficient way to capture these signals without blocking the main loop.

**Implementation Details:**
*   Attach interrupt service routines (ISRs) to the RC input pins (e.g., using `attachInterrupt()`).
*   Inside the ISRs, use `pulseIn()` to measure the pulse width. **Note:** `pulseIn()` can be blocking and might not be ideal inside an ISR for very fast loops. A more robust method involves using `RISING` and `FALLING` edge interrupts to record timestamps
  and calculate pulse width in the main loop or a dedicated timer interrupt.
*   Map the raw pulse width values (e.g., 1000-2000) to a normalized range (e.g., -1.0 to 1.0 or -max_angle to +max_angle) for the PID controller.

### 3.4. PID Control

PID (Proportional-Integral-Derivative) controllers are the core of the drone's stabilization system. Separate PID loops will be implemented for roll, pitch, and yaw. An additional PID loop might be used for altitude hold.

*   **Proportional (P):** Responds to the current error (difference between desired and actual angle). Larger error means larger correction.
*   **Integral (I):** Addresses steady-state errors by accumulating past errors. Helps eliminate persistent small errors.
*   **Derivative (D):** Responds to the rate of change of the error. Helps dampen oscillations and improve responsiveness.

**Implementation Details:**
*   Define `Kp`, `Ki`, `Kd` constants for each axis (Roll, Pitch, Yaw).
*   Calculate the error for each axis: `error = desired_angle - current_angle`.
*   Calculate the integral term: `integral += error * dt` (with anti-windup limits).
*   Calculate the derivative term: `derivative = (error - previous_error) / dt`.
*   Calculate PID output: `pid_output = Kp * error + Ki * integral + Kd * derivative`.
*   The `calculatePID()` function will take the current attitude (roll, pitch, yaw) and the desired attitude (from RC input) and produce the `pidRollOutput`, `pidPitchOutput`, `pidYawOutput` values.

### 3.5. Motor Control and Mixing

This module translates the combined throttle input and PID outputs into individual motor speed commands (PWM duty cycles) for each of the four ESCs. The motor mixing algorithm determines how much each motor needs to speed up or slow down to achieve the desired attitude changes.

**Implementation Details:**
*   Use ESP32's `ledc` peripheral for PWM generation. Configure the frequency (e.g., 500 Hz for standard ESCs) and resolution (e.g., 8-bit or 10-bit).
*   Attach PWM channels to the motor control pins using `ledcAttachPin()`.
*   The motor mixing algorithm will combine `rcThrottle` with `pidRollOutput`, `pidPitchOutput`, and `pidYawOutput` to calculate the final `motorFR`, `motorRL`, `motorFL`, `motorRR` values.
*   Ensure motor values are constrained within the valid PWM range for your ESCs (e.g., 1000-2000 microsecond pulse width, or corresponding duty cycle values).

### 3.6. GPS Integration (NEO-6M)

This module is responsible for reading and parsing data from the NEO-6M GPS module. The `TinyGPS++` library simplifies this process.

**Implementation Details:**
*   Initialize a `HardwareSerial` instance for the GPS module (e.g., `SerialGPS.begin(9600, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);`).
*   Continuously read incoming serial data from the GPS and feed it to `gps.encode()`.
*   Periodically check `gps.location.isValid()`, `gps.altitude.isValid()`, etc., to get updated latitude, longitude, and altitude.
*   The GPS data will be used for position hold and potentially for displaying coordinates on the TFT.

### 3.7. TFT Display Management (1.8 TFT SPI)

This module handles displaying real-time flight data and status information on the 1.8-inch TFT screen.

**Implementation Details:**
*   Initialize the `Adafruit_ST7735` and `Adafruit_GFX` libraries.
*   Use `tft.print()`, `tft.println()`, `tft.setCursor()`, `tft.setTextColor()`, etc., to draw text and graphics.
*   Update the display periodically (e.g., every 100-200ms) to show current roll, pitch, yaw, altitude, GPS status, battery voltage, and flight mode.

### 3.8. Failsafe Implementation (Conceptual)

While the detailed failsafe implementation will be covered in a later phase, the code structure should account for it. This involves monitoring critical parameters and taking predefined actions in case of signal loss or critical errors.

**Conceptual Implementation:**
*   Monitor RC signal presence. If signal is lost for a predefined duration, trigger failsafe.
*   Failsafe actions could include:
    *   Landing the drone gently.
    *   Holding current position (if GPS lock is available).
    *   Cutting motor power (as a last resort).

This comprehensive code structure provides a solid foundation for developing the quadcopter's flight control firmware. The next steps will involve implementing the details within each module, starting with sensor calibration and GPS integration.






## 3. Sensor Calibration and GPS Integration Procedures



# Sensor Calibration and GPS Integration Procedures

Accurate sensor data is fundamental for stable flight and reliable navigation in a quadcopter. This section details the calibration procedures for the GY-86 10DOF module (MPU6050, HMC5883L, MS5611) and the integration method for the NEO-6M GPS module, including its application for position hold.

## 1. GY-86 10DOF Module Calibration Procedures

Calibration is essential to compensate for manufacturing imperfections, sensor biases, and environmental influences. Each sensor within the GY-86 module requires a specific calibration process.

### 1.1. MPU6050 Accelerometer Calibration

The accelerometer measures linear acceleration along the X, Y, and Z axes. Calibration involves determining and compensating for static biases (offsets) that cause the sensor to report non-zero values even when stationary. The goal is to ensure that when the accelerometer is perfectly level, its X and Y axes read close to 0g, and its Z axis reads close to +1g (due to gravity).

**Procedure:**

1.  **Mounting:** Securely mount the GY-86 module to the drone frame in its final orientation. This is crucial because the calibration values are specific to the sensor\'s orientation relative to gravity.
2.  **Software Setup:** Load a dedicated accelerometer calibration sketch onto the ESP32. This sketch should read raw accelerometer values and send them to the Serial Monitor.
3.  **Data Collection:**
    *   Place the drone on a perfectly level surface. Ensure it is stable and not moving.
    *   Record a significant number of readings (e.g., 1000-2000 samples) for each axis (Ax, Ay, Az) while the drone is stationary and level. Calculate the average of these readings.
    *   Rotate the drone to six different orientations (each axis pointing up and down) and repeat the data collection and averaging for each orientation. The six orientations are: X-axis up, X-axis down, Y-axis up, Y-axis down, Z-axis up, Z-axis down.
        *   **X-axis up:** Place the drone so its X-axis points directly upwards.
        *   **X-axis down:** Place the drone so its X-axis points directly downwards.
        *   **Y-axis up:** Place the drone so its Y-axis points directly upwards.
        *   **Y-axis down:** Place the drone so its Y-axis points directly downwards.
        *   **Z-axis up:** Place the drone so its Z-axis points directly upwards (normal flying position).
        *   **Z-axis down:** Place the drone so its Z-axis points directly downwards.
4.  **Offset Calculation:**
    *   For each axis (X, Y, Z), the offset is calculated by averaging the readings from its positive and negative orientations and subtracting the expected gravitational value (0g for X and Y, +1g or -1g for Z depending on orientation).
    *   For example, `offset_x = (Ax_positive_g + Ax_negative_g) / 2`.
    *   The `+1g` and `-1g` values for the Z-axis (and 0g for X and Y) are the ideal readings when the sensor is perfectly aligned with gravity. The actual raw readings will deviate from these ideals.
    *   A common simplified approach for initial calibration is to take multiple readings with the drone perfectly level (Z-axis up) and calculate the average for each axis. These averages become your offsets. For example, if `Ax_avg = 150`, `Ay_avg = -70`, `Az_avg = 16384` (assuming 1g = 16384 for a 2g range), then `offset_x = 150`, `offset_y = -70`, `offset_z = 16384 - 16384 = 0` (if your raw Z is already 1g). More robust methods involve the 6-point calibration.
5.  **Apply Offsets:** Subtract the calculated offsets from all subsequent raw accelerometer readings in your flight control code. This will shift the readings so that when the sensor is stationary and level, X and Y are near zero, and Z is near the expected 1g.

**Example Code Snippet (Conceptual):**

```cpp
// In setup() or a dedicated calibration function
void calibrateAccelerometer() {
  long sumAx = 0, sumAy = 0, sumAz = 0;
  int numReadings = 1000;

  Serial.println("Calibrating Accelerometer... Keep drone still and level.");
  for (int i = 0; i < numReadings; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    sumAx += a.acceleration.x;
    sumAy += a.acceleration.y;
    sumAz += a.acceleration.z;
    delay(5); // Small delay between readings
  }

  accelOffsetX = sumAx / numReadings;
  accelOffsetY = sumAy / numReadings;
  // For Z-axis, if 1g is 16384 (for 2g range), and your average Az is 16400, offset is 16400 - 16384 = 16
  // This assumes the drone is level (Z-axis up) during this simple calibration.
  accelOffsetZ = (sumAz / numReadings) - 16384; // Adjust 16384 based on your MPU6050 sensitivity setting

  Serial.print("Accel Offsets: X="); Serial.print(accelOffsetX);
  Serial.print(", Y="); Serial.print(accelOffsetY);
  Serial.print(", Z="); Serial.println(accelOffsetZ);
}

// In readIMU() function:
// float calibratedAx = rawAx - accelOffsetX;
// float calibratedAy = rawAy - accelOffsetY;
// float calibratedAz = rawAz - accelOffsetZ;
```

### 1.2. MPU6050 Gyroscope Calibration

The gyroscope measures angular velocity. When the drone is stationary, the gyroscope should ideally report zero angular velocity. Calibration involves determining the static bias (drift) of the gyroscope.

**Procedure:**

1.  **Mounting:** Ensure the GY-86 module is securely mounted.
2.  **Software Setup:** Load a dedicated gyroscope calibration sketch. This sketch should read raw gyroscope values and send them to the Serial Monitor.
3.  **Data Collection:**
    *   Place the drone on a perfectly stable and level surface. Ensure it is absolutely still and not vibrating.
    *   Record a large number of readings (e.g., 2000-5000 samples) for each axis (Gx, Gy, Gz) while the drone is stationary.
    *   Calculate the average of these readings for each axis. These averages represent the gyroscope\'s static bias or offset.
4.  **Apply Offsets:** Subtract the calculated offsets from all subsequent raw gyroscope readings in your flight control code. This will zero out the gyroscope readings when the drone is stationary.

**Example Code Snippet (Conceptual):**

```cpp
// In setup() or a dedicated calibration function
void calibrateGyroscope() {
  long sumGx = 0, sumGy = 0, sumGz = 0;
  int numReadings = 2000;

  Serial.println("Calibrating Gyroscope... Keep drone absolutely still.");
  for (int i = 0; i < numReadings; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    sumGx += g.gyro.x;
    sumGy += g.gyro.y;
    sumGz += g.gyro.z;
    delay(2); // Small delay
  }

  gyroOffsetX = sumGx / numReadings;
  gyroOffsetY = sumGy / numReadings;
  gyroOffsetZ = sumGz / numReadings;

  Serial.print("Gyro Offsets: X="); Serial.print(gyroOffsetX);
  Serial.print(", Y="); Serial.print(gyroOffsetY);
  Serial.print(", Z="); Serial.println(gyroOffsetZ);
}

// In readIMU() function:
// float calibratedGx = rawGx - gyroOffsetX;
// float calibratedGy = rawGy - gyroOffsetY;
// float calibratedGz = rawGz - gyroOffsetZ;
```

### 1.3. HMC5883L Magnetometer Calibration

The magnetometer measures the Earth\'s magnetic field. Calibration is necessary to compensate for hard-iron (fixed magnetic fields from drone components) and soft-iron (distortion of the Earth\'s magnetic field by drone components) distortions. This typically involves moving the sensor in a figure-eight pattern to map the magnetic field in all directions.

**Procedure:**

1.  **Software Setup:** Load a magnetometer calibration sketch onto the ESP32. This sketch should read raw magnetometer values and send them to the Serial Monitor. It should also track the minimum and maximum values observed for each axis.
2.  **Data Collection (Figure-Eight Motion):**
    *   Hold the drone in your hand, away from any large metal objects or strong magnetic fields (e.g., speakers, power supplies).
    *   Slowly rotate the drone through all possible orientations, performing a 


figure-eight motion or rotating it around all three axes (roll, pitch, yaw) for at least 30-60 seconds. Ensure that the sensor experiences the full range of magnetic field variations.
    *   The calibration sketch should continuously record the minimum and maximum raw readings for X, Y, and Z axes.
3.  **Offset and Scale Factor Calculation:**
    *   **Hard-Iron Offsets:** Calculate the offset for each axis as `offset = (max_value + min_value) / 2`. These offsets represent the center of the magnetic sphere/ellipsoid.
    *   **Soft-Iron Scale Factors (Optional but Recommended):** For more accurate compensation, soft-iron distortions can be addressed. This involves calculating scale factors to transform the magnetic ellipsoid into a sphere. This is more complex and often requires a least-squares fitting algorithm or a dedicated calibration library. For a basic drone, hard-iron compensation might be sufficient.
4.  **Apply Offsets:** Subtract the calculated hard-iron offsets from all subsequent raw magnetometer readings in your flight control code.

**Example Code Snippet (Conceptual for Hard-Iron):**

```cpp
// In setup() or a dedicated calibration function
void calibrateMagnetometer() {
  Serial.println("Calibrating Magnetometer... Rotate drone in figure-eight pattern.");
  long magMinX = 0, magMaxX = 0;
  long magMinY = 0, magMaxY = 0;
  long magMinZ = 0, magMaxZ = 0;

  // Loop for a duration (e.g., 30 seconds) to collect data
  unsigned long startTime = millis();
  while (millis() - startTime < 30000) { // Collect data for 30 seconds
    sensors_event_t mag_event;
    mag.getEvent(&mag_event);

    // Update min/max values
    if (mag_event.magnetic.x < magMinX) magMinX = mag_event.magnetic.x;
    if (mag_event.magnetic.x > magMaxX) magMaxX = mag_event.magnetic.x;
    if (mag_event.magnetic.y < magMinY) magMinY = mag_event.magnetic.y;
    if (mag_event.magnetic.y > magMaxY) magMaxY = mag_event.magnetic.y;
    if (mag_event.magnetic.z < magMinZ) magMinZ = mag_event.magnetic.z;
    if (mag_event.magnetic.z > magMaxZ) magMaxZ = mag_event.magnetic.z;

    delay(10); // Small delay
  }

  magOffsetX = (magMaxX + magMinX) / 2;
  magOffsetY = (magMaxY + magMinY) / 2;
  magOffsetZ = (magMaxZ + magMinZ) / 2;

  Serial.print("Mag Offsets: X="); Serial.print(magOffsetX);
  Serial.print(", Y="); Serial.print(magOffsetY);
  Serial.print(", Z="); Serial.println(magOffsetZ);
}

// In readIMU() function:
// float calibratedMagX = rawMagX - magOffsetX;
// float calibratedMagY = rawMagY - magOffsetY;
// float calibratedMagZ = rawMagZ - magOffsetZ;
```

### 1.4. MS5611 Barometer Calibration

The MS5611 barometer provides pressure and temperature readings. While the sensor itself is factory calibrated, its readings are affected by ambient temperature and the drone\'s altitude. The primary calibration for a barometer in a drone context is to establish a reference pressure at ground level and to compensate for temperature drift.

**Procedure:**

1.  **Initial Reading:** Take an initial pressure reading when the drone is on the ground, before flight. This will serve as your reference pressure for altitude calculations.
2.  **Temperature Compensation:** The MS5611 provides internal temperature compensation, but external temperature changes can still affect accuracy. Ensure your code correctly uses the temperature readings provided by the MS5611 to calculate compensated pressure.
3.  **Altitude Calculation:** Altitude is calculated using the barometric formula, which relates pressure to altitude. The formula requires a reference pressure (pressure at sea level or ground level) and the current pressure.

    The simplified formula for altitude is:
    `Altitude (meters) = 44330 * (1 - (Current_Pressure / Reference_Pressure)^(1/5.255))`

    Where:
    *   `Current_Pressure` is the pressure reading from the MS5611.
    *   `Reference_Pressure` is the pressure at your starting altitude (e.g., ground level before takeoff).

**Example Code Snippet (Conceptual):**

```cpp
// Global variable
float groundPressure = 0;

void setupBarometer() {
  // Initialize MS5611
  // ...
  // Take initial ground pressure reading after a short delay for stabilization
  delay(1000);
  groundPressure = ms5611.readPressure();
  Serial.print("Ground Pressure: "); Serial.println(groundPressure);
}

void readBarometer() {
  // Read raw pressure and temperature
  pressure = ms5611.readPressure();
  temperature = ms5611.readTemperature();

  // Calculate altitude using the barometric formula
  // Ensure groundPressure is set before calculating altitude
  if (groundPressure > 0) {
    altitude = 44330.0 * (1.0 - pow(pressure / groundPressure, 1 / 5.255));
  }
}
```

## 2. GPS Integration Method for Position Hold

Integrating the NEO-6M GPS module allows the drone to maintain a stable position, a crucial feature for autonomous flight. Position hold works by using GPS data to detect drift and then applying corrections via the PID controller to bring the drone back to its target coordinates.

### 2.1. GPS Data Acquisition and Parsing

As outlined in the code structure, the `TinyGPS++` library simplifies parsing NMEA sentences from the NEO-6M module. The ESP32\'s `HardwareSerial` (UART2) will be used for communication.

**Key Steps:**

1.  **Initialize UART:** Configure `HardwareSerial` for the GPS module with the correct baud rate (typically 9600 for NEO-6M).
2.  **Read and Encode Data:** Continuously read bytes from the GPS serial port and feed them to `gps.encode()`. This function processes the NMEA sentences and updates the internal `TinyGPS++` object with the latest GPS data.
3.  **Validate Data:** Before using GPS data, always check its validity using functions like `gps.location.isValid()`, `gps.altitude.isValid()`, `gps.satellites.isValid()`, etc. This ensures you are using reliable data.
4.  **Extract Data:** Once valid, extract latitude, longitude, altitude, number of satellites, and HDOP (Horizontal Dilution of Precision) from the `TinyGPS++` object.

### 2.2. Position Hold Algorithm

Position hold typically involves two nested PID loops: one for horizontal position (X and Y axes) and another for altitude.

**Horizontal Position Hold (X-Y Plane):**

1.  **Set Target:** When position hold is activated (e.g., via an RC switch), the drone records its current GPS latitude and longitude as the target coordinates.
2.  **Calculate Error:** Continuously calculate the distance and bearing from the current GPS position to the target GPS position. This can be done using Haversine formula or simpler approximations for short distances.
    *   `distance_error_north = current_latitude - target_latitude`
    *   `distance_error_east = current_longitude - target_longitude`
    *   Convert these latitude/longitude differences into meters using appropriate conversion factors (e.g., 1 degree latitude is approx 111,320 meters; 1 degree longitude varies with latitude).
3.  **PID Control:** Apply separate PID controllers for the North-South (Pitch) and East-West (Roll) axes.
    *   **Pitch PID:** Takes `distance_error_north` as input and outputs a pitch correction (desired pitch angle).
    *   **Roll PID:** Takes `distance_error_east` as input and outputs a roll correction (desired roll angle).
4.  **Feed to Attitude PID:** The outputs from the position PID controllers (desired roll and pitch angles) are then fed as inputs to the main attitude PID controllers (which use IMU data to stabilize the drone).

**Altitude Hold (Z-Axis):**

1.  **Set Target:** When altitude hold is activated, the drone records its current altitude (from barometer or GPS altitude if available and reliable) as the target altitude.
2.  **Calculate Error:** Continuously calculate the difference between the current altitude and the target altitude: `altitude_error = target_altitude - current_altitude`.
3.  **PID Control:** Apply a PID controller for altitude.
    *   **Altitude PID:** Takes `altitude_error` as input and outputs a throttle correction. If the drone is too low, it increases throttle; if too high, it decreases throttle.
4.  **Feed to Throttle:** The output from the altitude PID controller is added to the base throttle value (e.g., from RC input) before being sent to the motors.

### 2.3. Considerations for GPS Integration

*   **GPS Lock:** Ensure the GPS module has a sufficient number of satellites (e.g., 6 or more) and a good HDOP value (e.g., less than 2.0) before attempting position hold. Display GPS status on the TFT screen.
*   **Interference:** Keep the GPS antenna away from noisy components (ESCs, motors, power lines) to minimize interference.
*   **Update Rate:** The NEO-6M\'s 1Hz update rate can be a limitation for very precise position hold. For faster response, a 5Hz update rate can be configured in the GPS module (if supported by your specific module and firmware, typically via u-center software).
*   **Sensor Fusion:** For more robust altitude hold, consider fusing barometer altitude with GPS altitude (if available) using a Kalman filter, as barometric altitude can drift with weather changes, and GPS altitude can be less accurate than horizontal position.
*   **Drift:** Even with position hold, some drift is inevitable due to GPS inaccuracies and environmental factors (wind). The PID gains for position hold will need careful tuning.

By following these calibration and integration procedures, you will ensure that your drone\'s sensors provide accurate data, and its GPS system can effectively contribute to stable flight and autonomous capabilities. The next phase will focus on configuring RC channel mapping and tuning PID control parameters.






## 3. Sensor Calibration and GPS Integration Procedures



# RC Channel Mapping and PID Control Parameters

This section covers the crucial steps of configuring the RC channel mapping for the FS-TH9X transmitter and defining the PID control parameters for stable flight. Proper RC mapping ensures intuitive control, while well-tuned PID parameters are essential for a responsive and stable quadcopter.

## 1. RC Channel Mapping for the FS-TH9X

RC channel mapping involves assigning the physical sticks and switches on your FS-TH9X transmitter to specific functions on the drone. The goal is to create a control scheme that is both intuitive and provides access to necessary flight modes and auxiliary functions.

### 1.1. Understanding RC Channels and Modes

The FS-TH9X is a 9-channel transmitter, but the included FS-R9B receiver typically has 8 channels. The four primary flight controls are:

*   **Throttle:** Controls the overall power to the motors, making the drone ascend or descend.
*   **Roll (Aileron):** Tilts the drone left or right.
*   **Pitch (Elevator):** Tilts the drone forward or backward.
*   **Yaw (Rudder):** Rotates the drone left or right.

There are different "modes" for how these controls are assigned to the transmitter sticks. The most common is **Mode 2**:

*   **Left Stick:**
    *   **Up/Down:** Throttle
    *   **Left/Right:** Yaw
*   **Right Stick:**
    *   **Up/Down:** Pitch
    *   **Left/Right:** Roll

Your FS-TH9X transmitter can be configured for different modes. Ensure it is set to the mode you are most comfortable with (we will assume Mode 2 for this guide).

### 1.2. Mapping Channels in the ESP32 Code

In the ESP32 flight control code, you will read the PWM signals from each receiver channel and map them to the corresponding control variables. The `pulseIn()` function or interrupt-based methods will give you a pulse width value, typically between 1000µs (microseconds) and 2000µs.

**Procedure:**

1.  **Connect Receiver to ESP32:** Connect the receiver channels to the ESP32 GPIO pins as defined in your wiring diagram.
2.  **Create a Test Sketch:** Write a simple Arduino sketch to read the PWM values from each receiver channel and print them to the Serial Monitor. This will help you verify which channel corresponds to which stick/switch on your transmitter.

    **Example Test Sketch:**

    ```cpp
    #define RC_CH1_PIN 13 // Roll
    #define RC_CH2_PIN 12 // Pitch
    #define RC_CH3_PIN 14 // Throttle
    #define RC_CH4_PIN 27 // Yaw
    #define RC_CH5_PIN 26 // Flight Mode

    void setup() {
      Serial.begin(115200);
      pinMode(RC_CH1_PIN, INPUT);
      pinMode(RC_CH2_PIN, INPUT);
      pinMode(RC_CH3_PIN, INPUT);
      pinMode(RC_CH4_PIN, INPUT);
      pinMode(RC_CH5_PIN, INPUT);
    }

    void loop() {
      int ch1 = pulseIn(RC_CH1_PIN, HIGH, 25000);
      int ch2 = pulseIn(RC_CH2_PIN, HIGH, 25000);
      int ch3 = pulseIn(RC_CH3_PIN, HIGH, 25000);
      int ch4 = pulseIn(RC_CH4_PIN, HIGH, 25000);
      int ch5 = pulseIn(RC_CH5_PIN, HIGH, 25000);

      Serial.print("Ch1 (Roll): "); Serial.print(ch1);
      Serial.print(" | Ch2 (Pitch): "); Serial.print(ch2);
      Serial.print(" | Ch3 (Throttle): "); Serial.print(ch3);
      Serial.print(" | Ch4 (Yaw): "); Serial.print(ch4);
      Serial.print(" | Ch5 (Mode): "); Serial.println(ch5);

      delay(100);
    }
    ```

3.  **Map Channels:** Move each stick and switch on your transmitter one at a time and observe which channel\"s value changes in the Serial Monitor. Record the channel number for each control.

4.  **Map Auxiliary Channels:** Use the remaining channels (e.g., Channel 5, 6) for auxiliary functions like:
    *   **Flight Mode Switching:** Assign a 3-position switch to a channel to switch between different flight modes (e.g., Acro/Rate Mode, Angle/Stabilize Mode, Position Hold Mode).
    *   **Arming/Disarming:** Use a switch to arm and disarm the motors for safety.
    *   **Buzzer/LEDs:** Trigger a buzzer or LEDs for notifications.

### 1.3. Normalizing RC Input

Once you have the raw PWM values (1000-2000µs), you need to normalize them into a range that is useful for the PID controllers. For example, you can map the roll, pitch, and yaw inputs to a desired angle range (e.g., -30 to +30 degrees) or a rate of rotation range (e.g., -200 to +200 degrees/second).

**Example Code Snippet (Conceptual):**

```cpp
// In your main loop or RC input processing function
void processRCInputs() {
  // Assuming rcRoll, rcPitch, rcYaw are raw PWM values (1000-2000)

  // Normalize to a range of -1.0 to 1.0
  float normalizedRoll = (rcRoll - 1500.0) / 500.0;
  float normalizedPitch = (rcPitch - 1500.0) / 500.0;
  float normalizedYaw = (rcYaw - 1500.0) / 500.0;

  // Map to desired angle or rate (example for angle control)
  float desiredRollAngle = normalizedRoll * MAX_ANGLE; // e.g., MAX_ANGLE = 30 degrees
  float desiredPitchAngle = normalizedPitch * MAX_ANGLE;
  float desiredYawRate = normalizedYaw * MAX_YAW_RATE; // e.g., MAX_YAW_RATE = 150 deg/s

  // Handle flight mode switching based on rcMode value
  if (rcMode < 1200) {
    flightMode = ACRO_MODE;
  } else if (rcMode < 1700) {
    flightMode = STABILIZE_MODE;
  } else {
    flightMode = POS_HOLD_MODE;
  }
}
```

## 2. PID Control Parameters for Stable Flight

PID (Proportional-Integral-Derivative) control is the heart of your drone\"s stability system. Tuning the PID parameters (`Kp`, `Ki`, `Kd`) is a critical and often iterative process. The goal is to find a balance between responsiveness and stability, without oscillations or sluggishness.

### 2.1. Understanding PID Components

*   **P (Proportional) Gain (`Kp`):** This is the primary gain that determines how strongly the drone responds to errors. A higher `Kp` will make the drone more responsive, but too high a value will cause oscillations.
*   **I (Integral) Gain (`Ki`):** This gain helps eliminate steady-state errors. It accumulates past errors and applies a correction to overcome persistent small drifts. A high `Ki` can lead to overshoot and slow oscillations.
*   **D (Derivative) Gain (`Kd`):** This gain acts as a damper, reducing oscillations and improving the drone\"s ability to hold its attitude. It responds to the rate of change of the error. A high `Kd` can amplify noise and cause high-frequency vibrations.

### 2.2. PID Tuning Procedure

PID tuning is best done in a safe, open area with the drone tethered or at a very low altitude. Always start with low PID values and gradually increase them.

**Recommended Tuning Order:**

1.  **Tune Roll and Pitch P-gains (`Kp_roll`, `Kp_pitch`):**
    *   Start with `Ki` and `Kd` set to zero for all axes.
    *   Begin with a low `Kp` value for roll and pitch (e.g., `Kp_roll = 1.0`, `Kp_pitch = 1.0`).
    *   Gradually increase `Kp` for roll and pitch. The drone should become more stable and responsive.
    *   Continue increasing `Kp` until you observe high-frequency oscillations. Then, reduce `Kp` by about 20-30% from that point.

2.  **Tune Roll and Pitch D-gains (`Kd_roll`, `Kd_pitch`):**
    *   With `Kp` set, start increasing `Kd` for roll and pitch.
    *   A higher `Kd` should help dampen the oscillations and make the drone feel more \"locked in.\"
    *   If you increase `Kd` too much, you may see high-frequency vibrations or hear a high-pitched noise from the motors. This is due to the D-term amplifying sensor noise. If this happens, reduce `Kd`.

3.  **Tune Roll and Pitch I-gains (`Ki_roll`, `Ki_pitch`):**
    *   With `Kp` and `Kd` set, start increasing `Ki` for roll and pitch.
    *   The I-term helps the drone hold its angle against external disturbances (like wind). A good `Ki` value will make the drone feel more stable and less prone to drifting.
    *   If `Ki` is too high, you will see slow, large oscillations (wobbles).

4.  **Tune Yaw P, I, and D gains (`Kp_yaw`, `Ki_yaw`, `Kd_yaw`):**
    *   The yaw axis is typically less critical than roll and pitch, and it often requires different PID values.
    *   Follow a similar procedure for tuning yaw: start with `Kp_yaw`, then `Kd_yaw`, and finally `Ki_yaw`.
    *   The yaw axis is often less prone to oscillation, so you may be able to use a higher `Kp_yaw`.

### 2.3. Initial PID Control Parameters (Starting Point)

PID values are highly dependent on the drone\"s frame, motors, propellers, weight, and other factors. The following are **example starting points** and will almost certainly need to be adjusted for your specific build.

**Example Initial PID Values:**

| Parameter      | Roll          | Pitch         | Yaw           |
| :------------- | :------------ | :------------ | :------------ |
| **Kp**         | 1.2           | 1.2           | 2.0           |
| **Ki**         | 0.02          | 0.02          | 0.1           |
| **Kd**         | 0.5           | 0.5           | 0.2           |

**PID Implementation in Code (Conceptual):**

```cpp
// PID constants (to be tuned)
float Kp_roll = 1.2, Ki_roll = 0.02, Kd_roll = 0.5;
float Kp_pitch = 1.2, Ki_pitch = 0.02, Kd_pitch = 0.5;
float Kp_yaw = 2.0, Ki_yaw = 0.1, Kd_yaw = 0.2;

// PID variables
float error_roll, prev_error_roll, integral_roll, derivative_roll, pid_roll_output;
float error_pitch, prev_error_pitch, integral_pitch, derivative_pitch, pid_pitch_output;
float error_yaw, prev_error_yaw, integral_yaw, derivative_yaw, pid_yaw_output;

unsigned long last_pid_time = 0;

void calculatePID() {
  unsigned long current_time = micros();
  float dt = (current_time - last_pid_time) / 1000000.0; // Time difference in seconds
  last_pid_time = current_time;

  // --- Roll PID ---
  error_roll = desired_roll_angle - current_roll_angle;
  integral_roll += error_roll * dt;
  derivative_roll = (error_roll - prev_error_roll) / dt;
  pid_roll_output = Kp_roll * error_roll + Ki_roll * integral_roll + Kd_roll * derivative_roll;
  prev_error_roll = error_roll;

  // --- Pitch PID ---
  error_pitch = desired_pitch_angle - current_pitch_angle;
  integral_pitch += error_pitch * dt;
  derivative_pitch = (error_pitch - prev_error_pitch) / dt;
  pid_pitch_output = Kp_pitch * error_pitch + Ki_pitch * integral_pitch + Kd_pitch * derivative_pitch;
  prev_error_pitch = error_pitch;

  // --- Yaw PID ---
  error_yaw = desired_yaw_rate - current_yaw_rate; // Yaw is often controlled by rate
  integral_yaw += error_yaw * dt;
  derivative_yaw = (error_yaw - prev_error_yaw) / dt;
  pid_yaw_output = Kp_yaw * error_yaw + Ki_yaw * integral_yaw + Kd_yaw * derivative_yaw;
  prev_error_yaw = error_yaw;

  // Anti-windup for integral terms (constrain integral to a reasonable range)
  integral_roll = constrain(integral_roll, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_pitch = constrain(integral_pitch, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_yaw = constrain(integral_yaw, -INTE_LIMIT, INTEGRAL_LIMIT);
}
```

### 2.4. PID Tuning Tips

*   **Tune one axis at a time:** It can be helpful to focus on tuning roll first, then pitch, then yaw.
*   **Use a flight mode switch:** Set up a switch on your transmitter to toggle between a known stable set of PID values and the new values you are testing. This allows you to quickly revert if the new values are unstable.
*   **Record your flights:** If possible, record video of your tuning flights to review the drone\"s behavior and identify oscillations or other issues.
*   **Telemetry:** Use the TFT display or a serial connection to monitor real-time PID outputs and sensor data. This can provide valuable insights during tuning.
*   **Be patient:** PID tuning is an art as much as a science. It takes time and practice to get it right.

By carefully mapping your RC channels and methodically tuning your PID parameters, you will be well on your way to achieving a stable and responsive quadcopter. The next phase will focus on implementing failsafe mechanisms to ensure the safety of your drone and its surroundings.






## 4. RC Channel Mapping and PID Control Parameters

This section covers the crucial steps of configuring the RC channel mapping for the FS-TH9X transmitter and defining the PID control parameters for stable flight. Proper RC mapping ensures intuitive control, while well-tuned PID parameters are essential for a responsive and stable quadcopter.

### 4.1. RC Channel Mapping for the FS-TH9X

RC channel mapping involves assigning the physical sticks and switches on your FS-TH9X transmitter to specific functions on the drone. The goal is to create a control scheme that is both intuitive and provides access to necessary flight modes and auxiliary functions.

#### 4.1.1. Understanding RC Channels and Modes

The FS-TH9X is a 9-channel transmitter, but the included FS-R9B receiver typically has 8 channels. The four primary flight controls are:

*   **Throttle:** Controls the overall power to the motors, making the drone ascend or descend.
*   **Roll (Aileron):** Tilts the drone left or right.
*   **Pitch (Elevator):** Tilts the drone forward or backward.
*   **Yaw (Rudder):** Rotates the drone left or right.

There are different "modes" for how these controls are assigned to the transmitter sticks. The most common is **Mode 2**:

*   **Left Stick:**
    *   **Up/Down:** Throttle
    *   **Left/Right:** Yaw
*   **Right Stick:**
    *   **Up/Down:** Pitch
    *   **Left/Right:** Roll

Your FS-TH9X transmitter can be configured for different modes. Ensure it is set to the mode you are most comfortable with (we will assume Mode 2 for this guide).

#### 4.1.2. Mapping Channels in the ESP32 Code

In the ESP32 flight control code, you will read the PWM signals from each receiver channel and map them to the corresponding control variables. The `pulseIn()` function or interrupt-based methods will give you a pulse width value, typically between 1000µs (microseconds) and 2000µs.

**Procedure:**

1.  **Connect Receiver to ESP32:** Connect the receiver channels to the ESP32 GPIO pins as defined in your wiring diagram.
2.  **Create a Test Sketch:** Write a simple Arduino sketch to read the PWM values from each receiver channel and print them to the Serial Monitor. This will help you verify which channel corresponds to which stick/switch on your transmitter.

    **Example Test Sketch:**

    ```cpp
    #define RC_CH1_PIN 13 // Roll
    #define RC_CH2_PIN 12 // Pitch
    #define RC_CH3_PIN 14 // Throttle
    #define RC_CH4_PIN 27 // Yaw
    #define RC_CH5_PIN 26 // Flight Mode

    void setup() {
      Serial.begin(115200);
      pinMode(RC_CH1_PIN, INPUT);
      pinMode(RC_CH2_PIN, INPUT);
      pinMode(RC_CH3_PIN, INPUT);
      pinMode(RC_CH4_PIN, INPUT);
      pinMode(RC_CH5_PIN, INPUT);
    }

    void loop() {
      int ch1 = pulseIn(RC_CH1_PIN, HIGH, 25000);
      int ch2 = pulseIn(RC_CH2_PIN, HIGH, 25000);
      int ch3 = pulseIn(RC_CH3_PIN, HIGH, 25000);
      int ch4 = pulseIn(RC_CH4_PIN, HIGH, 25000);
      int ch5 = pulseIn(RC_CH5_PIN, HIGH, 25000);

      Serial.print("Ch1 (Roll): "); Serial.print(ch1);
      Serial.print(" | Ch2 (Pitch): "); Serial.print(ch2);
      Serial.print(" | Ch3 (Throttle): "); Serial.print(ch3);
      Serial.print(" | Ch4 (Yaw): "); Serial.print(ch4);
      Serial.print(" | Ch5 (Mode): "); Serial.println(ch5);

      delay(100);
    }
    ```

3.  **Map Channels:** Move each stick and switch on your transmitter one at a time and observe which channel's value changes in the Serial Monitor. Record the channel number for each control.

4.  **Map Auxiliary Channels:** Use the remaining channels (e.g., Channel 5, 6) for auxiliary functions like:
    *   **Flight Mode Switching:** Assign a 3-position switch to a channel to switch between different flight modes (e.g., Acro/Rate Mode, Angle/Stabilize Mode, Position Hold Mode).
    *   **Arming/Disarming:** Use a switch to arm and disarm the motors for safety.
    *   **Buzzer/LEDs:** Trigger a buzzer or LEDs for notifications.

#### 4.1.3. Normalizing RC Input

Once you have the raw PWM values (1000-2000µs), you need to normalize them into a range that is useful for the PID controllers. For example, you can map the roll, pitch, and yaw inputs to a desired angle range (e.g., -30 to +30 degrees) or a rate of rotation range (e.g., -200 to +200 degrees/second).

**Example Code Snippet (Conceptual):**

```cpp
// In your main loop or RC input processing function
void processRCInputs() {
  // Assuming rcRoll, rcPitch, rcYaw are raw PWM values (1000-2000)

  // Normalize to a range of -1.0 to 1.0
  float normalizedRoll = (rcRoll - 1500.0) / 500.0;
  float normalizedPitch = (rcPitch - 1500.0) / 500.0;
  float normalizedYaw = (rcYaw - 1500.0) / 500.0;

  // Map to desired angle or rate (example for angle control)
  float desiredRollAngle = normalizedRoll * MAX_ANGLE; // e.g., MAX_ANGLE = 30 degrees
  float desiredPitchAngle = normalizedPitch * MAX_ANGLE;
  float desiredYawRate = normalizedYaw * MAX_YAW_RATE; // e.g., MAX_YAW_RATE = 150 deg/s

  // Handle flight mode switching based on rcMode value
  if (rcMode < 1200) {
    flightMode = ACRO_MODE;
  } else if (rcMode < 1700) {
    flightMode = STABILIZE_MODE;
  } else {
    flightMode = POS_HOLD_MODE;
  }
}
```

### 4.2. PID Control Parameters for Stable Flight

PID (Proportional-Integral-Derivative) control is the heart of your drone's stability system. Tuning the PID parameters (`Kp`, `Ki`, `Kd`) is a critical and often iterative process. The goal is to find a balance between responsiveness and stability, without oscillations or sluggishness.

#### 4.2.1. Understanding PID Components

*   **P (Proportional) Gain (`Kp`):** This is the primary gain that determines how strongly the drone responds to errors. A higher `Kp` will make the drone more responsive, but too high a value will cause oscillations.
*   **I (Integral) Gain (`Ki`):** This gain helps eliminate steady-state errors. It accumulates past errors and applies a correction to overcome persistent small drifts. A high `Ki` can lead to overshoot and slow oscillations.
*   **D (Derivative) Gain (`Kd`):** This gain acts as a damper, reducing oscillations and improving the drone's ability to hold its attitude. It responds to the rate of change of the error. A high `Kd` can amplify noise and cause high-frequency vibrations.

#### 4.2.2. PID Tuning Procedure

PID tuning is best done in a safe, open area with the drone tethered or at a very low altitude. Always start with low PID values and gradually increase them.

**Recommended Tuning Order:**

1.  **Tune Roll and Pitch P-gains (`Kp_roll`, `Kp_pitch`):**
    *   Start with `Ki` and `Kd` set to zero for all axes.
    *   Begin with a low `Kp` value for roll and pitch (e.g., `Kp_roll = 1.0`, `Kp_pitch = 1.0`).
    *   Gradually increase `Kp` for roll and pitch. The drone should become more stable and responsive.
    *   Continue increasing `Kp` until you observe high-frequency oscillations. Then, reduce `Kp` by about 20-30% from that point.

2.  **Tune Roll and Pitch D-gains (`Kd_roll`, `Kd_pitch`):**
    *   With `Kp` set, start increasing `Kd` for roll and pitch.
    *   A higher `Kd` should help dampen the oscillations and make the drone feel more "locked in."
    *   If you increase `Kd` too much, you may see high-frequency vibrations or hear a high-pitched noise from the motors. This is due to the D-term amplifying sensor noise. If this happens, reduce `Kd`.

3.  **Tune Roll and Pitch I-gains (`Ki_roll`, `Ki_pitch`):**
    *   With `Kp` and `Kd` set, start increasing `Ki` for roll and pitch.
    *   The I-term helps the drone hold its angle against external disturbances (like wind). A good `Ki` value will make the drone feel more stable and less prone to drifting.
    *   If `Ki` is too high, you will see slow, large oscillations (wobbles).

4.  **Tune Yaw P, I, and D gains (`Kp_yaw`, `Ki_yaw`, `Kd_yaw`):**
    *   The yaw axis is typically less critical than roll and pitch, and it often requires different PID values.
    *   Follow a similar procedure for tuning yaw: start with `Kp_yaw`, then `Kd_yaw`, and finally `Ki_yaw`.
    *   The yaw axis is often less prone to oscillation, so you may be able to use a higher `Kp_yaw`.

#### 4.2.3. Initial PID Control Parameters (Starting Point)

PID values are highly dependent on the drone's frame, motors, propellers, weight, and other factors. The following are **example starting points** and will almost certainly need to be adjusted for your specific build.

**Example Initial PID Values:**

| Parameter      | Roll          | Pitch         | Yaw           |
| :------------- | :------------ | :------------ | :------------ |
| **Kp**         | 1.2           | 1.2           | 2.0           |
| **Ki**         | 0.02          | 0.02          | 0.1           |
| **Kd**         | 0.5           | 0.5           | 0.2           |

**PID Implementation in Code (Conceptual):**

```cpp
// PID constants (to be tuned)
float Kp_roll = 1.2, Ki_roll = 0.02, Kd_roll = 0.5;
float Kp_pitch = 1.2, Ki_pitch = 0.02, Kd_pitch = 0.5;
float Kp_yaw = 2.0, Ki_yaw = 0.1, Kd_yaw = 0.2;

// PID variables
float error_roll, prev_error_roll, integral_roll, derivative_roll, pid_roll_output;
float error_pitch, prev_error_pitch, integral_pitch, derivative_pitch, pid_pitch_output;
float error_yaw, prev_error_yaw, integral_yaw, derivative_yaw, pid_yaw_output;

unsigned long last_pid_time = 0;

void calculatePID() {
  unsigned long current_time = micros();
  float dt = (current_time - last_pid_time) / 1000000.0; // Time difference in seconds
  last_pid_time = current_time;

  // --- Roll PID ---
  error_roll = desired_roll_angle - current_roll_angle;
  integral_roll += error_roll * dt;
  derivative_roll = (error_roll - prev_error_roll) / dt;
  pid_roll_output = Kp_roll * error_roll + Ki_roll * integral_roll + Kd_roll * derivative_roll;
  prev_error_roll = error_roll;

  // --- Pitch PID ---
  error_pitch = desired_pitch_angle - current_pitch_angle;
  integral_pitch += error_pitch * dt;
  derivative_pitch = (error_pitch - prev_error_pitch) / dt;
  pid_pitch_output = Kp_pitch * error_pitch + Ki_pitch * integral_pitch + Kd_pitch * derivative_pitch;
  prev_error_pitch = error_pitch;

  // --- Yaw PID ---
  error_yaw = desired_yaw_rate - current_yaw_rate; // Yaw is often controlled by rate
  integral_yaw += error_yaw * dt;
  derivative_yaw = (error_yaw - prev_error_yaw) / dt;
  pid_yaw_output = Kp_yaw * error_yaw + Ki_yaw * integral_yaw + Kd_yaw * derivative_yaw;
  prev_error_yaw = error_yaw;

  // Anti-windup for integral terms (constrain integral to a reasonable range)
  integral_roll = constrain(integral_roll, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_pitch = constrain(integral_pitch, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_yaw = constrain(integral_yaw, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
}
```

#### 4.2.4. PID Tuning Tips

*   **Tune one axis at a time:** It can be helpful to focus on tuning roll first, then pitch, then yaw.
*   **Use a flight mode switch:** Set up a switch on your transmitter to toggle between a known stable set of PID values and the new values you are testing. This allows you to quickly revert if the new values are unstable.
*   **Record your flights:** If possible, record video of your tuning flights to review the drone's behavior and identify oscillations or other issues.
*   **Telemetry:** Use the TFT display or a serial connection to monitor real-time PID outputs and sensor data. This can provide valuable insights during tuning.
*   **Be patient:** PID tuning is an art as much as a science. It takes time and practice to get it right.

By carefully mapping your RC channels and methodically tuning your PID parameters, you will be well on your way to achieving a stable and responsive quadcopter. The next phase will focus on implementing failsafe mechanisms to ensure the safety of your drone and its surroundings.




# RC Channel Mapping and PID Control Parameters

This section covers the crucial steps of configuring the RC channel mapping for the FS-TH9X transmitter and defining the PID control parameters for stable flight. Proper RC mapping ensures intuitive control, while well-tuned PID parameters are essential for a responsive and stable quadcopter.

## 1. RC Channel Mapping for the FS-TH9X

RC channel mapping involves assigning the physical sticks and switches on your FS-TH9X transmitter to specific functions on the drone. The goal is to create a control scheme that is both intuitive and provides access to necessary flight modes and auxiliary functions.

### 1.1. Understanding RC Channels and Modes

The FS-TH9X is a 9-channel transmitter, but the included FS-R9B receiver typically has 8 channels. The four primary flight controls are:

*   **Throttle:** Controls the overall power to the motors, making the drone ascend or descend.
*   **Roll (Aileron):** Tilts the drone left or right.
*   **Pitch (Elevator):** Tilts the drone forward or backward.
*   **Yaw (Rudder):** Rotates the drone left or right.

There are different "modes" for how these controls are assigned to the transmitter sticks. The most common is **Mode 2**:

*   **Left Stick:**
    *   **Up/Down:** Throttle
    *   **Left/Right:** Yaw
*   **Right Stick:**
    *   **Up/Down:** Pitch
    *   **Left/Right:** Roll

Your FS-TH9X transmitter can be configured for different modes. Ensure it is set to the mode you are most comfortable with (we will assume Mode 2 for this guide).

### 1.2. Mapping Channels in the ESP32 Code

In the ESP32 flight control code, you will read the PWM signals from each receiver channel and map them to the corresponding control variables. The `pulseIn()` function or interrupt-based methods will give you a pulse width value, typically between 1000µs (microseconds) and 2000µs.

**Procedure:**

1.  **Connect Receiver to ESP32:** Connect the receiver channels to the ESP32 GPIO pins as defined in your wiring diagram.
2.  **Create a Test Sketch:** Write a simple Arduino sketch to read the PWM values from each receiver channel and print them to the Serial Monitor. This will help you verify which channel corresponds to which stick/switch on your transmitter.

    **Example Test Sketch:**

    ```cpp
    #define RC_CH1_PIN 13 // Roll
    #define RC_CH2_PIN 12 // Pitch
    #define RC_CH3_PIN 14 // Throttle
    #define RC_CH4_PIN 27 // Yaw
    #define RC_CH5_PIN 26 // Flight Mode

    void setup() {
      Serial.begin(115200);
      pinMode(RC_CH1_PIN, INPUT);
      pinMode(RC_CH2_PIN, INPUT);
      pinMode(RC_CH3_PIN, INPUT);
      pinMode(RC_CH4_PIN, INPUT);
      pinMode(RC_CH5_PIN, INPUT);
    }

    void loop() {
      int ch1 = pulseIn(RC_CH1_PIN, HIGH, 25000);
      int ch2 = pulseIn(RC_CH2_PIN, HIGH, 25000);
      int ch3 = pulseIn(RC_CH3_PIN, HIGH, 25000);
      int ch4 = pulseIn(RC_CH4_PIN, HIGH, 25000);
      int ch5 = pulseIn(RC_CH5_PIN, HIGH, 25000);

      Serial.print("Ch1 (Roll): "); Serial.print(ch1);
      Serial.print(" | Ch2 (Pitch): "); Serial.print(ch2);
      Serial.print(" | Ch3 (Throttle): "); Serial.print(ch3);
      Serial.print(" | Ch4 (Yaw): "); Serial.print(ch4);
      Serial.print(" | Ch5 (Mode): "); Serial.println(ch5);

      delay(100);
    }
    ```

3.  **Map Channels:** Move each stick and switch on your transmitter one at a time and observe which channel's value changes in the Serial Monitor. Record the channel number for each control.

4.  **Map Auxiliary Channels:** Use the remaining channels (e.g., Channel 5, 6) for auxiliary functions like:
    *   **Flight Mode Switching:** Assign a 3-position switch to a channel to switch between different flight modes (e.g., Acro/Rate Mode, Angle/Stabilize Mode, Position Hold Mode).
    *   **Arming/Disarming:** Use a switch to arm and disarm the motors for safety.
    *   **Buzzer/LEDs:** Trigger a buzzer or LEDs for notifications.

### 1.3. Normalizing RC Input

Once you have the raw PWM values (1000-2000µs), you need to normalize them into a range that is useful for the PID controllers. For example, you can map the roll, pitch, and yaw inputs to a desired angle range (e.g., -30 to +30 degrees) or a rate of rotation range (e.g., -200 to +200 degrees/second).

**Example Code Snippet (Conceptual):**

```cpp
// In your main loop or RC input processing function
void processRCInputs() {
  // Assuming rcRoll, rcPitch, rcYaw are raw PWM values (1000-2000)

  // Normalize to a range of -1.0 to 1.0
  float normalizedRoll = (rcRoll - 1500.0) / 500.0;
  float normalizedPitch = (rcPitch - 1500.0) / 500.0;
  float normalizedYaw = (rcYaw - 1500.0) / 500.0;

  // Map to desired angle or rate (example for angle control)
  float desiredRollAngle = normalizedRoll * MAX_ANGLE; // e.g., MAX_ANGLE = 30 degrees
  float desiredPitchAngle = normalizedPitch * MAX_ANGLE;
  float desiredYawRate = normalizedYaw * MAX_YAW_RATE; // e.g., MAX_YAW_RATE = 150 deg/s

  // Handle flight mode switching based on rcMode value
  if (rcMode < 1200) {
    flightMode = ACRO_MODE;
  } else if (rcMode < 1700) {
    flightMode = STABILIZE_MODE;
  } else {
    flightMode = POS_HOLD_MODE;
  }
}
```

## 2. PID Control Parameters for Stable Flight

PID (Proportional-Integral-Derivative) control is the heart of your drone's stability system. Tuning the PID parameters (`Kp`, `Ki`, `Kd`) is a critical and often iterative process. The goal is to find a balance between responsiveness and stability, without oscillations or sluggishness.

### 2.1. Understanding PID Components

*   **P (Proportional) Gain (`Kp`):** This is the primary gain that determines how strongly the drone responds to errors. A higher `Kp` will make the drone more responsive, but too high a value will cause oscillations.
*   **I (Integral) Gain (`Ki`):** This gain helps eliminate steady-state errors. It accumulates past errors and applies a correction to overcome persistent small drifts. A high `Ki` can lead to overshoot and slow oscillations.
*   **D (Derivative) Gain (`Kd`):** This gain acts as a damper, reducing oscillations and improving the drone's ability to hold its attitude. It responds to the rate of change of the error. A high `Kd` can amplify noise and cause high-frequency vibrations.

### 2.2. PID Tuning Procedure

PID tuning is best done in a safe, open area with the drone tethered or at a very low altitude. Always start with low PID values and gradually increase them.

**Recommended Tuning Order:**

1.  **Tune Roll and Pitch P-gains (`Kp_roll`, `Kp_pitch`):**
    *   Start with `Ki` and `Kd` set to zero for all axes.
    *   Begin with a low `Kp` value for roll and pitch (e.g., `Kp_roll = 1.0`, `Kp_pitch = 1.0`).
    *   Gradually increase `Kp` for roll and pitch. The drone should become more stable and responsive.
    *   Continue increasing `Kp` until you observe high-frequency oscillations. Then, reduce `Kp` by about 20-30% from that point.

2.  **Tune Roll and Pitch D-gains (`Kd_roll`, `Kd_pitch`):**
    *   With `Kp` set, start increasing `Kd` for roll and pitch.
    *   A higher `Kd` should help dampen the oscillations and make the drone feel more "locked in."
    *   If you increase `Kd` too much, you may see high-frequency vibrations or hear a high-pitched noise from the motors. This is due to the D-term amplifying sensor noise. If this happens, reduce `Kd`.

3.  **Tune Roll and Pitch I-gains (`Ki_roll`, `Ki_pitch`):**
    *   With `Kp` and `Kd` set, start increasing `Ki` for roll and pitch.
    *   The I-term helps the drone hold its angle against external disturbances (like wind). A good `Ki` value will make the drone feel more stable and less prone to drifting.
    *   If `Ki` is too high, you will see slow, large oscillations (wobbles).

4.  **Tune Yaw P, I, and D gains (`Kp_yaw`, `Ki_yaw`, `Kd_yaw`):**
    *   The yaw axis is typically less critical than roll and pitch, and it often requires different PID values.
    *   Follow a similar procedure for tuning yaw: start with `Kp_yaw`, then `Kd_yaw`, and finally `Ki_yaw`.
    *   The yaw axis is often less prone to oscillation, so you may be able to use a higher `Kp_yaw`.

### 2.3. Initial PID Control Parameters (Starting Point)

PID values are highly dependent on the drone's frame, motors, propellers, weight, and other factors. The following are **example starting points** and will almost certainly need to be adjusted for your specific build.

**Example Initial PID Values:**

| Parameter      | Roll          | Pitch         | Yaw           |
| :------------- | :------------ | :------------ | :------------ |
| **Kp**         | 1.2           | 1.2           | 2.0           |
| **Ki**         | 0.02          | 0.02          | 0.1           |
| **Kd**         | 0.5           | 0.5           | 0.2           |

**PID Implementation in Code (Conceptual):**

```cpp
// PID constants (to be tuned)
float Kp_roll = 1.2, Ki_roll = 0.02, Kd_roll = 0.5;
float Kp_pitch = 1.2, Ki_pitch = 0.02, Kd_pitch = 0.5;
float Kp_yaw = 2.0, Ki_yaw = 0.1, Kd_yaw = 0.2;

// PID variables
float error_roll, prev_error_roll, integral_roll, derivative_roll, pid_roll_output;
float error_pitch, prev_error_pitch, integral_pitch, derivative_pitch, pid_pitch_output;
float error_yaw, prev_error_yaw, integral_yaw, derivative_yaw, pid_yaw_output;

unsigned long last_pid_time = 0;

void calculatePID() {
  unsigned long current_time = micros();
  float dt = (current_time - last_pid_time) / 1000000.0; // Time difference in seconds
  last_pid_time = current_time;

  // --- Roll PID ---
  error_roll = desired_roll_angle - current_roll_angle;
  integral_roll += error_roll * dt;
  derivative_roll = (error_roll - prev_error_roll) / dt;
  pid_roll_output = Kp_roll * error_roll + Ki_roll * integral_roll + Kd_roll * derivative_roll;
  prev_error_roll = error_roll;

  // --- Pitch PID ---
  error_pitch = desired_pitch_angle - current_pitch_angle;
  integral_pitch += error_pitch * dt;
  derivative_pitch = (error_pitch - prev_error_pitch) / dt;
  pid_pitch_output = Kp_pitch * error_pitch + Ki_pitch * integral_pitch + Kd_pitch * derivative_pitch;
  prev_error_pitch = error_pitch;

  // --- Yaw PID ---
  error_yaw = desired_yaw_rate - current_yaw_rate; // Yaw is often controlled by rate
  integral_yaw += error_yaw * dt;
  derivative_yaw = (error_yaw - prev_error_yaw) / dt;
  pid_yaw_output = Kp_yaw * error_yaw + Ki_yaw * integral_yaw + Kd_yaw * derivative_yaw;
  prev_error_yaw = error_yaw;

  // Anti-windup for integral terms (constrain integral to a reasonable range)
  integral_roll = constrain(integral_roll, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_pitch = constrain(integral_pitch, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
  integral_yaw = constrain(integral_yaw, -INTEGRAL_LIMIT, INTEGRAL_LIMIT);
}
```

### 2.4. PID Tuning Tips

*   **Tune one axis at a time:** It can be helpful to focus on tuning roll first, then pitch, then yaw.
*   **Use a flight mode switch:** Set up a switch on your transmitter to toggle between a known stable set of PID values and the new values you are testing. This allows you to quickly revert if the new values are unstable.
*   **Record your flights:** If possible, record video of your tuning flights to review the drone's behavior and identify oscillations or other issues.
*   **Telemetry:** Use the TFT display or a serial connection to monitor real-time PID outputs and sensor data. This can provide valuable insights during tuning.
*   **Be patient:** PID tuning is an art as much as a science. It takes time and practice to get it right.

By carefully mapping your RC channels and methodically tuning your PID parameters, you will be well on your way to achieving a stable and responsive quadcopter. The next phase will focus on implementing failsafe mechanisms to ensure the safety of your drone and its surroundings.






## 5. Failsafe Implementation Requirements

Failsafe mechanisms are critical safety features for any unmanned aerial vehicle (UAV), especially a DIY quadcopter. They are designed to mitigate risks and prevent accidents in the event of unexpected conditions, such as loss of radio control signal, low battery voltage, or sensor failures. Implementing robust failsafe protocols is paramount for protecting the drone, property, and people.

### 5.1. General Principles of Failsafe

*   **Prioritize Safety:** The primary goal of any failsafe action is to bring the drone to a safe state, even if it means a controlled landing or motor shutdown.
*   **Redundancy:** Where possible, implement multiple layers of failsafe for critical systems.
*   **Clear Indication:** The drone should provide clear visual or auditory indications when a failsafe condition is detected and activated.
*   **Configurability:** Allow for configurable failsafe actions and thresholds to adapt to different flight environments and pilot preferences.
*   **Testing:** Thoroughly test all failsafe scenarios in a controlled environment before actual flight.

### 5.2. Key Failsafe Scenarios and Implementation

#### 5.2.1. RC Signal Loss Failsafe

This is one of the most common and critical failsafe scenarios. If the drone loses communication with the RC transmitter, it must take a predefined safe action.

**Implementation Requirements:**

1.  **Signal Monitoring:** The ESP32 must continuously monitor the incoming PWM signals from the FS-TH9X receiver. If the pulse width for any critical channel (Throttle, Roll, Pitch, Yaw) drops to zero, becomes erratic, or remains outside a valid range (e.g., 900-2100µs) for a specified duration, it indicates signal loss.
2.  **Timeout Detection:** Implement a timeout mechanism. If no valid signal is received for a short period (e.g., 0.5 to 1 second), trigger the failsafe.
3.  **Failsafe Actions (Configurable):**
    *   **Land in Place:** This is often the preferred action. The drone gradually reduces throttle and lands vertically at its current position. This requires the drone to maintain attitude control during the descent.
        *   **Procedure:** Upon signal loss, set target roll, pitch, and yaw to zero (level flight). Gradually decrease the throttle output over a few seconds until the drone lands. Once landed, disarm the motors.
    *   **Return-to-Launch (RTL):** If GPS lock is strong and reliable, the drone can attempt to fly back to its takeoff location and land. This is more complex and requires a working GPS module and a robust navigation algorithm.
        *   **Procedure:** Upon signal loss, if GPS is valid, set the target coordinates to the recorded home position. The drone will then navigate towards the home position and initiate an auto-land sequence upon arrival.
    *   **Hover and Wait:** The drone maintains its current altitude and position (if GPS is available) for a short period, hoping the signal returns. If the signal does not return within a set time, it proceeds to land.
    *   **Motor Shutdown (Last Resort):** In extreme cases or if other failsafe actions are not possible, immediately cut power to the motors. This is a last resort as it will result in an uncontrolled crash, but it might be necessary to prevent further damage or injury if the drone is behaving erratically.
4.  **Re-arming:** If the RC signal returns while the drone is in a failsafe state (e.g., landing), the pilot should be able to regain control. However, the drone should not immediately jump back to the last commanded state. It should require a specific re-arming sequence (e.g., throttle low, yaw left) to prevent accidental motor spin-up.

**Code Snippet (Conceptual for Signal Loss Detection):**

```cpp
unsigned long lastRCSignalTime = 0;
bool rcSignalLost = false;
const unsigned long RC_TIMEOUT_MS = 1000; // 1 second timeout

// In your RC input processing function (or ISRs)
void updateRCSignalStatus() {
  // Assuming rcThrottle, rcRoll, etc., are updated by interrupts
  // If any critical channel receives a valid pulse, update timestamp
  if (rcThrottle > 900 && rcThrottle < 2100) { // Check for valid pulse width
    lastRCSignalTime = millis();
    if (rcSignalLost) {
      rcSignalLost = false;
      Serial.println("RC Signal Recovered!");
      // Potentially transition out of failsafe or wait for re-arm
    }
  }
}

// In loop()
void checkRCSignalFailsafe() {
  if (millis() - lastRCSignalTime > RC_TIMEOUT_MS && !rcSignalLost) {
    rcSignalLost = true;
    Serial.println("RC Signal Lost! Initiating Failsafe...");
    // Trigger failsafe action (e.g., start auto-land sequence)
    initiateFailsafeLand();
  }
}
```

#### 5.2.2. Low Battery Voltage Failsafe

Flying with a critically low battery can lead to sudden power loss and an uncontrolled crash. A low battery failsafe is essential to prevent this.

**Implementation Requirements:**

1.  **Voltage Monitoring:** Implement a voltage divider circuit to measure the drone's main battery voltage using an analog input pin on the ESP32. Continuously read and average the battery voltage.
2.  **Thresholds:** Define multiple voltage thresholds:
    *   **Warning Threshold:** (e.g., 3.5V per cell for LiPo). When voltage drops below this, provide a visual warning (e.g., flashing LED, message on TFT display) to the pilot.
    *   **Critical Threshold:** (e.g., 3.3V per cell for LiPo). When voltage drops below this, initiate an automatic landing sequence.
3.  **Failsafe Action:** Upon reaching the critical threshold, the drone should initiate a controlled landing, similar to the RC signal loss failsafe. It should not attempt to return to launch as this might consume too much power.
4.  **Motor Shutdown:** If the voltage drops to an extremely low level (e.g., 3.0V per cell), immediately cut motor power to prevent battery damage.

**Code Snippet (Conceptual for Battery Monitoring):**

```cpp
#define BATTERY_VOLTAGE_PIN 34 // Example analog pin
const float VOLTAGE_DIVIDER_RATIO = 11.0; // Adjust based on your resistor values
const float WARNING_VOLTAGE = 10.5; // For 3S LiPo (3.5V/cell * 3)
const float CRITICAL_VOLTAGE = 9.9; // For 3S LiPo (3.3V/cell * 3)

float currentBatteryVoltage = 0;

void readBatteryVoltage() {
  int rawValue = analogRead(BATTERY_VOLTAGE_PIN);
  // Convert raw ADC reading to voltage
  // ESP32 ADC resolution is 12-bit (0-4095), default Vref is 3.3V
  currentBatteryVoltage = (rawValue / 4095.0) * 3.3 * VOLTAGE_DIVIDER_RATIO;

  if (currentBatteryVoltage < WARNING_VOLTAGE && currentBatteryVoltage > CRITICAL_VOLTAGE) {
    // Display warning on TFT, flash LED
    tft.println("LOW BATTERY!");
  } else if (currentBatteryVoltage < CRITICAL_VOLTAGE) {
    Serial.println("CRITICAL BATTERY! Initiating Failsafe Land...");
    initiateFailsafeLand();
  }
}
```

#### 5.2.3. Sensor Failure Failsafe

While less common, a critical sensor failure (e.g., IMU stops responding, GPS data becomes invalid) can lead to unstable flight. Detecting and responding to these failures is important.

**Implementation Requirements:**

1.  **Sensor Health Monitoring:** Periodically check if sensors are providing valid data. For I2C/SPI sensors, check communication status (e.g., `Wire.endTransmission()` return value, `sensor.begin()` return value). For GPS, check `gps.location.isValid()` and `gps.satellites.value()`.
2.  **Redundancy (if possible):** For critical sensors like the IMU, consider using a secondary sensor or a robust sensor fusion algorithm that can gracefully degrade if one sensor fails.
3.  **Failsafe Action:** If a critical sensor fails during flight, the safest action is usually an immediate controlled landing or, if the drone is very low, a motor shutdown to prevent it from flying away erratically.

#### 5.2.4. Arming/Disarming Safety

To prevent accidental motor spin-up, implement a strict arming and disarming procedure.

**Implementation Requirements:**

1.  **Disarmed State:** Upon power-up, the motors must be disarmed and will not spin regardless of throttle input.
2.  **Arming Sequence:** Require a specific stick combination (e.g., throttle low, yaw right for 2 seconds) or a dedicated switch on the RC transmitter to arm the motors. Provide visual/auditory feedback (e.g., LEDs, buzzer) when armed.
3.  **Disarming Sequence:** Allow disarming via a stick combination (e.g., throttle low, yaw left) or the same dedicated switch. Motors should immediately stop.
4.  **Automatic Disarm:** Automatically disarm motors if:
    *   Throttle is at minimum for a prolonged period (e.g., 5 seconds) while armed but not flying.
    *   The drone is inverted.
    *   A critical failsafe (signal loss, low battery) is triggered.

#### 5.2.5. Pre-Flight Checks

Implement a series of checks that must pass before the drone can be armed.

**Implementation Requirements:**

1.  **Sensor Initialization:** Verify all sensors (IMU, Barometer, GPS) have initialized successfully.
2.  **RC Signal Presence:** Ensure a valid RC signal is being received.
3.  **Battery Voltage:** Check that the battery voltage is above the warning threshold.
4.  **Level Check:** Ensure the drone is relatively level before arming (e.g., roll and pitch angles are within +/- 5 degrees).
5.  **GPS Lock (for GPS modes):** If GPS-dependent flight modes are enabled, ensure a sufficient GPS lock (number of satellites, HDOP) before allowing arming into those modes.

By carefully designing and implementing these failsafe mechanisms, you can significantly enhance the safety and reliability of your DIY quadcopter. The final phase will involve outlining the initial setup, testing, and troubleshooting procedures to get your drone ready for its maiden flight.




# Failsafe Implementation Requirements

Failsafe mechanisms are critical safety features for any unmanned aerial vehicle (UAV), especially a DIY quadcopter. They are designed to mitigate risks and prevent accidents in the event of unexpected conditions, such as loss of radio control signal, low battery voltage, or sensor failures. Implementing robust failsafe protocols is paramount for protecting the drone, property, and people.

## 1. General Principles of Failsafe

*   **Prioritize Safety:** The primary goal of any failsafe action is to bring the drone to a safe state, even if it means a controlled landing or motor shutdown.
*   **Redundancy:** Where possible, implement multiple layers of failsafe for critical systems.
*   **Clear Indication:** The drone should provide clear visual or auditory indications when a failsafe condition is detected and activated.
*   **Configurability:** Allow for configurable failsafe actions and thresholds to adapt to different flight environments and pilot preferences.
*   **Testing:** Thoroughly test all failsafe scenarios in a controlled environment before actual flight.

## 2. Key Failsafe Scenarios and Implementation

### 2.1. RC Signal Loss Failsafe

This is one of the most common and critical failsafe scenarios. If the drone loses communication with the RC transmitter, it must take a predefined safe action.

**Implementation Requirements:**

1.  **Signal Monitoring:** The ESP32 must continuously monitor the incoming PWM signals from the FS-TH9X receiver. If the pulse width for any critical channel (Throttle, Roll, Pitch, Yaw) drops to zero, becomes erratic, or remains outside a valid range (e.g., 900-2100µs) for a specified duration, it indicates signal loss.
2.  **Timeout Detection:** Implement a timeout mechanism. If no valid signal is received for a short period (e.g., 0.5 to 1 second), trigger the failsafe.
3.  **Failsafe Actions (Configurable):**
    *   **Land in Place:** This is often the preferred action. The drone gradually reduces throttle and lands vertically at its current position. This requires the drone to maintain attitude control during the descent.
        *   **Procedure:** Upon signal loss, set target roll, pitch, and yaw to zero (level flight). Gradually decrease the throttle output over a few seconds until the drone lands. Once landed, disarm the motors.
    *   **Return-to-Launch (RTL):** If GPS lock is strong and reliable, the drone can attempt to fly back to its takeoff location and land. This is more complex and requires a working GPS module and a robust navigation algorithm.
        *   **Procedure:** Upon signal loss, if GPS is valid, set the target coordinates to the recorded home position. The drone will then navigate towards the home position and initiate an auto-land sequence upon arrival.
    *   **Hover and Wait:** The drone maintains its current altitude and position (if GPS is available) for a short period, hoping the signal returns. If the signal does not return within a set time, it proceeds to land.
    *   **Motor Shutdown (Last Resort):** In extreme cases or if other failsafe actions are not possible, immediately cut power to the motors. This is a last resort as it will result in an uncontrolled crash, but it might be necessary to prevent further damage or injury if the drone is behaving erratically.
4.  **Re-arming:** If the RC signal returns while the drone is in a failsafe state (e.g., landing), the pilot should be able to regain control. However, the drone should not immediately jump back to the last commanded state. It should require a specific re-arming sequence (e.g., throttle low, yaw left) to prevent accidental motor spin-up.

**Code Snippet (Conceptual for Signal Loss Detection):**

```cpp
unsigned long lastRCSignalTime = 0;
bool rcSignalLost = false;
const unsigned long RC_TIMEOUT_MS = 1000; // 1 second timeout

// In your RC input processing function (or ISRs)
void updateRCSignalStatus() {
  // Assuming rcThrottle, rcRoll, etc., are updated by interrupts
  // If any critical channel receives a valid pulse, update timestamp
  if (rcThrottle > 900 && rcThrottle < 2100) { // Check for valid pulse width
    lastRCSignalTime = millis();
    if (rcSignalLost) {
      rcSignalLost = false;
      Serial.println("RC Signal Recovered!");
      // Potentially transition out of failsafe or wait for re-arm
    }
  }
}

// In loop()
void checkRCSignalFailsafe() {
  if (millis() - lastRCSignalTime > RC_TIMEOUT_MS && !rcSignalLost) {
    rcSignalLost = true;
    Serial.println("RC Signal Lost! Initiating Failsafe...");
    // Trigger failsafe action (e.g., start auto-land sequence)
    initiateFailsafeLand();
  }
}
```

### 2.2. Low Battery Voltage Failsafe

Flying with a critically low battery can lead to sudden power loss and an uncontrolled crash. A low battery failsafe is essential to prevent this.

**Implementation Requirements:**

1.  **Voltage Monitoring:** Implement a voltage divider circuit to measure the drone's main battery voltage using an analog input pin on the ESP32. Continuously read and average the battery voltage.
2.  **Thresholds:** Define multiple voltage thresholds:
    *   **Warning Threshold:** (e.g., 3.5V per cell for LiPo). When voltage drops below this, provide a visual warning (e.g., flashing LED, message on TFT display) to the pilot.
    *   **Critical Threshold:** (e.g., 3.3V per cell for LiPo). When voltage drops below this, initiate an automatic landing sequence.
3.  **Failsafe Action:** Upon reaching the critical threshold, the drone should initiate a controlled landing, similar to the RC signal loss failsafe. It should not attempt to return to launch as this might consume too much power.
4.  **Motor Shutdown:** If the voltage drops to an extremely low level (e.g., 3.0V per cell), immediately cut motor power to prevent battery damage.

**Code Snippet (Conceptual for Battery Monitoring):**

```cpp
#define BATTERY_VOLTAGE_PIN 34 // Example analog pin
const float VOLTAGE_DIVIDER_RATIO = 11.0; // Adjust based on your resistor values
const float WARNING_VOLTAGE = 10.5; // For 3S LiPo (3.5V/cell * 3)
const float CRITICAL_VOLTAGE = 9.9; // For 3S LiPo (3.3V/cell * 3)

float currentBatteryVoltage = 0;

void readBatteryVoltage() {
  int rawValue = analogRead(BATTERY_VOLTAGE_PIN);
  // Convert raw ADC reading to voltage
  // ESP32 ADC resolution is 12-bit (0-4095), default Vref is 3.3V
  currentBatteryVoltage = (rawValue / 4095.0) * 3.3 * VOLTAGE_DIVIDER_RATIO;

  if (currentBatteryVoltage < WARNING_VOLTAGE && currentBatteryVoltage > CRITICAL_VOLTAGE) {
    // Display warning on TFT, flash LED
    tft.println("LOW BATTERY!");
  } else if (currentBatteryVoltage < CRITICAL_VOLTAGE) {
    Serial.println("CRITICAL BATTERY! Initiating Failsafe Land...");
    initiateFailsafeLand();
  }
}
```

### 2.3. Sensor Failure Failsafe

While less common, a critical sensor failure (e.g., IMU stops responding, GPS data becomes invalid) can lead to unstable flight. Detecting and responding to these failures is important.

**Implementation Requirements:**

1.  **Sensor Health Monitoring:** Periodically check if sensors are providing valid data. For I2C/SPI sensors, check communication status (e.g., `Wire.endTransmission()` return value, `sensor.begin()` return value). For GPS, check `gps.location.isValid()` and `gps.satellites.value()`.
2.  **Redundancy (if possible):** For critical sensors like the IMU, consider using a secondary sensor or a robust sensor fusion algorithm that can gracefully degrade if one sensor fails.
3.  **Failsafe Action:** If a critical sensor fails during flight, the safest action is usually an immediate controlled landing or, if the drone is very low, a motor shutdown to prevent it from flying away erratically.

### 2.4. Arming/Disarming Safety

To prevent accidental motor spin-up, implement a strict arming and disarming procedure.

**Implementation Requirements:**

1.  **Disarmed State:** Upon power-up, the motors must be disarmed and will not spin regardless of throttle input.
2.  **Arming Sequence:** Require a specific stick combination (e.g., throttle low, yaw right for 2 seconds) or a dedicated switch on the RC transmitter to arm the motors. Provide visual/auditory feedback (e.g., LEDs, buzzer) when armed.
3.  **Disarming Sequence:** Allow disarming via a stick combination (e.g., throttle low, yaw left) or the same dedicated switch. Motors should immediately stop.
4.  **Automatic Disarm:** Automatically disarm motors if:
    *   Throttle is at minimum for a prolonged period (e.g., 5 seconds) while armed but not flying.
    *   The drone is inverted.
    *   A critical failsafe (signal loss, low battery) is triggered.

### 2.5. Pre-Flight Checks

Implement a series of checks that must pass before the drone can be armed.

**Implementation Requirements:**

1.  **Sensor Initialization:** Verify all sensors (IMU, Barometer, GPS) have initialized successfully.
2.  **RC Signal Presence:** Ensure a valid RC signal is being received.
3.  **Battery Voltage:** Check that the battery voltage is above the warning threshold.
4.  **Level Check:** Ensure the drone is relatively level before arming (e.g., roll and pitch angles are within +/- 5 degrees).
5.  **GPS Lock (for GPS modes):** If GPS-dependent flight modes are enabled, ensure a sufficient GPS lock (number of satellites, HDOP) before allowing arming into those modes.

By carefully designing and implementing these failsafe mechanisms, you can significantly enhance the safety and reliability of your DIY quadcopter. The final phase will involve outlining the initial setup, testing, and troubleshooting procedures to get your drone ready for its maiden flight.






## 5. Failsafe Implementation Requirements

Failsafe mechanisms are critical safety features for any unmanned aerial vehicle (UAV), especially a DIY quadcopter. They are designed to mitigate risks and prevent accidents in the event of unexpected conditions, such as loss of radio control signal, low battery voltage, or sensor failures. Implementing robust failsafe protocols is paramount for protecting the drone, property, and people.

### 5.1. General Principles of Failsafe

*   **Prioritize Safety:** The primary goal of any failsafe action is to bring the drone to a safe state, even if it means a controlled landing or motor shutdown.
*   **Redundancy:** Where possible, implement multiple layers of failsafe for critical systems.
*   **Clear Indication:** The drone should provide clear visual or auditory indications when a failsafe condition is detected and activated.
*   **Configurability:** Allow for configurable failsafe actions and thresholds to adapt to different flight environments and pilot preferences.
*   **Testing:** Thoroughly test all failsafe scenarios in a controlled environment before actual flight.

### 5.2. Key Failsafe Scenarios and Implementation

#### 5.2.1. RC Signal Loss Failsafe

This is one of the most common and critical failsafe scenarios. If the drone loses communication with the RC transmitter, it must take a predefined safe action.

**Implementation Requirements:**

1.  **Signal Monitoring:** The ESP32 must continuously monitor the incoming PWM signals from the FS-TH9X receiver. If the pulse width for any critical channel (Throttle, Roll, Pitch, Yaw) drops to zero, becomes erratic, or remains outside a valid range (e.g., 900-2100µs) for a specified duration, it indicates signal loss.
2.  **Timeout Detection:** Implement a timeout mechanism. If no valid signal is received for a short period (e.g., 0.5 to 1 second), trigger the failsafe.
3.  **Failsafe Actions (Configurable):**
    *   **Land in Place:** This is often the preferred action. The drone gradually reduces throttle and lands vertically at its current position. This requires the drone to maintain attitude control during the descent.
        *   **Procedure:** Upon signal loss, set target roll, pitch, and yaw to zero (level flight). Gradually decrease the throttle output over a few seconds until the drone lands. Once landed, disarm the motors.
    *   **Return-to-Launch (RTL):** If GPS lock is strong and reliable, the drone can attempt to fly back to its takeoff location and land. This is more complex and requires a working GPS module and a robust navigation algorithm.
        *   **Procedure:** Upon signal loss, if GPS is valid, set the target coordinates to the recorded home position. The drone will then navigate towards the home position and initiate an auto-land sequence upon arrival.
    *   **Hover and Wait:** The drone maintains its current altitude and position (if GPS is available) for a short period, hoping the signal returns. If the signal does not return within a set time, it proceeds to land.
    *   **Motor Shutdown (Last Resort):** In extreme cases or if other failsafe actions are not possible, immediately cut power to the motors. This is a last resort as it will result in an uncontrolled crash, but it might be necessary to prevent further damage or injury if the drone is behaving erratically.
4.  **Re-arming:** If the RC signal returns while the drone is in a failsafe state (e.g., landing), the pilot should be able to regain control. However, the drone should not immediately jump back to the last commanded state. It should require a specific re-arming sequence (e.g., throttle low, yaw left) to prevent accidental motor spin-up.

**Code Snippet (Conceptual for Signal Loss Detection):**

```cpp
unsigned long lastRCSignalTime = 0;
bool rcSignalLost = false;
const unsigned long RC_TIMEOUT_MS = 1000; // 1 second timeout

// In your RC input processing function (or ISRs)
void updateRCSignalStatus() {
  // Assuming rcThrottle, rcRoll, etc., are updated by interrupts
  // If any critical channel receives a valid pulse, update timestamp
  if (rcThrottle > 900 && rcThrottle < 2100) { // Check for valid pulse width
    lastRCSignalTime = millis();
    if (rcSignalLost) {
      rcSignalLost = false;
      Serial.println("RC Signal Recovered!");
      // Potentially transition out of failsafe or wait for re-arm
    }
  }
}

// In loop()
void checkRCSignalFailsafe() {
  if (millis() - lastRCSignalTime > RC_TIMEOUT_MS && !rcSignalLost) {
    rcSignalLost = true;
    Serial.println("RC Signal Lost! Initiating Failsafe...");
    // Trigger failsafe action (e.g., start auto-land sequence)
    initiateFailsafeLand();
  }
}
```

#### 5.2.2. Low Battery Voltage Failsafe

Flying with a critically low battery can lead to sudden power loss and an uncontrolled crash. A low battery failsafe is essential to prevent this.

**Implementation Requirements:**

1.  **Voltage Monitoring:** Implement a voltage divider circuit to measure the drone's main battery voltage using an analog input pin on the ESP32. Continuously read and average the battery voltage.
2.  **Thresholds:** Define multiple voltage thresholds:
    *   **Warning Threshold:** (e.g., 3.5V per cell for LiPo). When voltage drops below this, provide a visual warning (e.g., flashing LED, message on TFT display) to the pilot.
    *   **Critical Threshold:** (e.g., 3.3V per cell for LiPo). When voltage drops below this, initiate an automatic landing sequence.
3.  **Failsafe Action:** Upon reaching the critical threshold, the drone should initiate a controlled landing, similar to the RC signal loss failsafe. It should not attempt to return to launch as this might consume too much power.
4.  **Motor Shutdown:** If the voltage drops to an extremely low level (e.g., 3.0V per cell), immediately cut motor power to prevent battery damage.

**Code Snippet (Conceptual for Battery Monitoring):**

```cpp
#define BATTERY_VOLTAGE_PIN 34 // Example analog pin
const float VOLTAGE_DIVIDER_RATIO = 11.0; // Adjust based on your resistor values
const float WARNING_VOLTAGE = 10.5; // For 3S LiPo (3.5V/cell * 3)
const float CRITICAL_VOLTAGE = 9.9; // For 3S LiPo (3.3V/cell * 3)

float currentBatteryVoltage = 0;

void readBatteryVoltage() {
  int rawValue = analogRead(BATTERY_VOLTAGE_PIN);
  // Convert raw ADC reading to voltage
  // ESP32 ADC resolution is 12-bit (0-4095), default Vref is 3.3V
  currentBatteryVoltage = (rawValue / 4095.0) * 3.3 * VOLTAGE_DIVIDER_RATIO;

  if (currentBatteryVoltage < WARNING_VOLTAGE && currentBatteryVoltage > CRITICAL_VOLTAGE) {
    // Display warning on TFT, flash LED
    tft.println("LOW BATTERY!");
  } else if (currentBatteryVoltage < CRITICAL_VOLTAGE) {
    Serial.println("CRITICAL BATTERY! Initiating Failsafe Land...");
    initiateFailsafeLand();
  }
}
```

#### 5.2.3. Sensor Failure Failsafe

While less common, a critical sensor failure (e.g., IMU stops responding, GPS data becomes invalid) can lead to unstable flight. Detecting and responding to these failures is important.

**Implementation Requirements:**

1.  **Sensor Health Monitoring:** Periodically check if sensors are providing valid data. For I2C/SPI sensors, check communication status (e.g., `Wire.endTransmission()` return value, `sensor.begin()` return value). For GPS, check `gps.location.isValid()` and `gps.satellites.value()`.
2.  **Redundancy (if possible):** For critical sensors like the IMU, consider using a secondary sensor or a robust sensor fusion algorithm that can gracefully degrade if one sensor fails.
3.  **Failsafe Action:** If a critical sensor fails during flight, the safest action is usually an immediate controlled landing or, if the drone is very low, a motor shutdown to prevent it from flying away erratically.

#### 5.2.4. Arming/Disarming Safety

To prevent accidental motor spin-up, implement a strict arming and disarming procedure.

**Implementation Requirements:**

1.  **Disarmed State:** Upon power-up, the motors must be disarmed and will not spin regardless of throttle input.
2.  **Arming Sequence:** Require a specific stick combination (e.g., throttle low, yaw right for 2 seconds) or a dedicated switch on the RC transmitter to arm the motors. Provide visual/auditory feedback (e.g., LEDs, buzzer) when armed.
3.  **Disarming Sequence:** Allow disarming via a stick combination (e.g., throttle low, yaw left) or the same dedicated switch. Motors should immediately stop.
4.  **Automatic Disarm:** Automatically disarm motors if:
    *   Throttle is at minimum for a prolonged period (e.g., 5 seconds) while armed but not flying.
    *   The drone is inverted.
    *   A critical failsafe (signal loss, low battery) is triggered.

#### 5.2.5. Pre-Flight Checks

Implement a series of checks that must pass before the drone can be armed.

**Implementation Requirements:**

1.  **Sensor Initialization:** Verify all sensors (IMU, Barometer, GPS) have initialized successfully.
2.  **RC Signal Presence:** Ensure a valid RC signal is being received.
3.  **Battery Voltage:** Check that the battery voltage is above the warning threshold.
4.  **Level Check:** Ensure the drone is relatively level before arming (e.g., roll and pitch angles are within +/- 5 degrees).
5.  **GPS Lock (for GPS modes):** If GPS-dependent flight modes are enabled, ensure a sufficient GPS lock (number of satellites, HDOP) before allowing arming into those modes.

By carefully designing and implementing these failsafe mechanisms, you can significantly enhance the safety and reliability of your DIY quadcopter. The final phase will involve outlining the initial setup, testing, and troubleshooting procedures to get your drone ready for its maiden flight.






## 6. Initial Setup, Testing, and Troubleshooting Procedures



# Initial Setup, Testing, and Troubleshooting Procedures

This section outlines the initial setup, testing, and troubleshooting procedures for your DIY quadcopter. Following these steps carefully will help ensure a successful build and safe operation.

## 6.1 Initial Hardware Assembly and Connections Check

1.  **Review Wiring Diagram:** Double-check all connections against the detailed wiring diagram provided in Section 1.6. Ensure correct polarity for power connections (VCC/3.3V/5V to VCC, GND to GND) and proper data line connections (SDA to SDA, SCL to SCL, TX to RX, RX to TX, MOSI to MOSI, MISO to MISO, SCK to SCK, CS to CS, PWM to PWM input on ESCs).
2.  **Secure Components:** Mount all components (ESP32, GY-86, NEO-6M, RC receiver, TFT display) securely to the quadcopter frame using standoffs, zip ties, or double-sided tape. Ensure they are isolated from vibrations as much as possible, especially the IMU.
3.  **Motor and ESC Placement:** Ensure motors are mounted correctly (e.g., CW and CCW motors in appropriate positions) and ESCs are placed to allow for good airflow and cooling.
4.  **Propeller Removal:** **CRITICALLY IMPORTANT:** Before any testing or power-up, **REMOVE ALL PROPELLERS** from the motors. This is a crucial safety measure to prevent accidental injury.

## 6.2 First Power-Up and Basic Connectivity Test

1.  **Power Source:** Connect a suitable power source (e.g., a LiPo battery with a power distribution board or a regulated DC power supply) to your quadcopter's power system. Ensure the voltage is appropriate for your ESCs and motors.
2.  **ESP32 Power-Up:** Observe the ESP32. It should power on, and its built-in LED (usually blue) might blink or light up, indicating it's receiving power.
3.  **TFT Display Test:** If your ESP32 code includes basic display initialization, the 1.8" TFT SPI display should light up or show some initial text/graphics. If not, check SPI connections and power to the display.
4.  **GY-86 and NEO-6M Power:** The GY-86 and NEO-6M modules should also show signs of power (e.g., LEDs on the modules). The NEO-6M GPS module's LED will typically blink slowly when searching for satellites and light up steadily when a GPS fix is acquired.

## 6.3 ESP32 Firmware Upload

1.  **Connect ESP32 to PC:** Connect your ESP32 development board to your computer using a USB cable.
2.  **Arduino IDE/PlatformIO:** Open your Arduino IDE or PlatformIO project. Ensure you have selected the correct ESP32 board (e.g., 


# Initial Setup, Testing, and Troubleshooting Procedures

This section outlines the initial setup, testing, and troubleshooting procedures for your DIY quadcopter. Following these steps carefully will help ensure a successful build and safe operation.

## 6.1 Initial Hardware Assembly and Connections Check

1.  **Review Wiring Diagram:** Double-check all connections against the detailed wiring diagram provided in Section 1.6. Ensure correct polarity for power connections (VCC/3.3V/5V to VCC, GND to GND) and proper data line connections (SDA to SDA, SCL to SCL, TX to RX, RX to TX, MOSI to MOSI, MISO to MISO, SCK to SCK, CS to CS, PWM to PWM input on ESCs).
2.  **Secure Components:** Mount all components (ESP32, GY-86, NEO-6M, RC receiver, TFT display) securely to the quadcopter frame using standoffs, zip ties, or double-sided tape. Ensure they are isolated from vibrations as much as possible, especially the IMU.
3.  **Motor and ESC Placement:** Ensure motors are mounted correctly (e.g., CW and CCW motors in appropriate positions) and ESCs are placed to allow for good airflow and cooling.
4.  **Propeller Removal:** **CRITICALLY IMPORTANT:** Before any testing or power-up, **REMOVE ALL PROPELLERS** from the motors. This is a crucial safety measure to prevent accidental injury.

## 6.2 First Power-Up and Basic Connectivity Test

1.  **Power Source:** Connect a suitable power source (e.g., a LiPo battery with a power distribution board or a regulated DC power supply) to your quadcopter's power system. Ensure the voltage is appropriate for your ESCs and motors.
2.  **ESP32 Power-Up:** Observe the ESP32. It should power on, and its built-in LED (usually blue) might blink or light up, indicating it's receiving power.
3.  **TFT Display Test:** If your ESP32 code includes basic display initialization, the 1.8" TFT SPI display should light up or show some initial text/graphics. If not, check SPI connections and power to the display.
4.  **GY-86 and NEO-6M Power:** The GY-86 and NEO-6M modules should also show signs of power (e.g., LEDs on the modules). The NEO-6M GPS module's LED will typically blink slowly when searching for satellites and light up steadily when a GPS fix is acquired.

## 6.3 ESP32 Firmware Upload

1.  **Connect ESP32 to PC:** Connect your ESP32 development board to your computer using a USB cable.
2.  **Arduino IDE/PlatformIO:** Open your Arduino IDE or PlatformIO project. Ensure you have selected the correct ESP32 board (e.g., 




## 6. Initial Setup, Testing, and Troubleshooting Procedures






## 5. Failsafe Implementation Requirements




# Failsafe Implementation Requirements

Failsafe mechanisms are critical safety features for any unmanned aerial vehicle (UAV), especially a DIY quadcopter. They are designed to mitigate risks and prevent accidents in the event of unexpected conditions, such as loss of radio control signal, low battery voltage, or sensor failures. Implementing robust failsafe protocols is paramount for protecting the drone, property, and people.

## 1. General Principles of Failsafe

*   **Prioritize Safety:** The primary goal of any failsafe action is to bring the drone to a safe state, even if it means a controlled landing or motor shutdown.
*   **Redundancy:** Where possible, implement multiple layers of failsafe for critical systems.
*   **Clear Indication:** The drone should provide clear visual or auditory indications when a failsafe condition is detected and activated.
*   **Configurability:** Allow for configurable failsafe actions and thresholds to adapt to different flight environments and pilot preferences.
*   **Testing:** Thoroughly test all failsafe scenarios in a controlled environment before actual flight.

## 2. Key Failsafe Scenarios and Implementation

### 2.1. RC Signal Loss Failsafe

This is one of the most common and critical failsafe scenarios. If the drone loses communication with the RC transmitter, it must take a predefined safe action.

**Implementation Requirements:**

1.  **Signal Monitoring:** The ESP32 must continuously monitor the incoming PWM signals from the FS-TH9X receiver. If the pulse width for any critical channel (Throttle, Roll, Pitch, Yaw) drops to zero, becomes erratic, or remains outside a valid range (e.g., 900-2100µs) for a specified duration, it indicates signal loss.
2.  **Timeout Detection:** Implement a timeout mechanism. If no valid signal is received for a short period (e.g., 0.5 to 1 second), trigger the failsafe.
3.  **Failsafe Actions (Configurable):**
    *   **Land in Place:** This is often the preferred action. The drone gradually reduces throttle and lands vertically at its current position. This requires the drone to maintain attitude control during the descent.
        *   **Procedure:** Upon signal loss, set target roll, pitch, and yaw to zero (level flight). Gradually decrease the throttle output over a few seconds until the drone lands. Once landed, disarm the motors.
    *   **Return-to-Launch (RTL):** If GPS lock is strong and reliable, the drone can attempt to fly back to its takeoff location and land. This is more complex and requires a working GPS module and a robust navigation algorithm.
        *   **Procedure:** Upon signal loss, if GPS is valid, set the target coordinates to the recorded home position. The drone will then navigate towards the home position and initiate an auto-land sequence upon arrival.
    *   **Hover and Wait:** The drone maintains its current altitude and position (if GPS is available) for a short period, hoping the signal returns. If the signal does not return within a set time, it proceeds to land.
    *   **Motor Shutdown (Last Resort):** In extreme cases or if other failsafe actions are not possible, immediately cut power to the motors. This is a last resort as it will result in an uncontrolled crash, but it might be necessary to prevent further damage or injury if the drone is behaving erratically.
4.  **Re-arming:** If the RC signal returns while the drone is in a failsafe state (e.g., landing), the pilot should be able to regain control. However, the drone should not immediately jump back to the last commanded state. It should require a specific re-arming sequence (e.g., throttle low, yaw left) to prevent accidental motor spin-up.

**Code Snippet (Conceptual for Signal Loss Detection):**

```cpp
unsigned long lastRCSignalTime = 0;
bool rcSignalLost = false;
const unsigned long RC_TIMEOUT_MS = 1000; // 1 second timeout

// In your RC input processing function (or ISRs)
void updateRCSignalStatus() {
  // Assuming rcThrottle, rcRoll, etc., are updated by interrupts
  // If any critical channel receives a valid pulse, update timestamp
  if (rcThrottle > 900 && rcThrottle < 2100) { // Check for valid pulse width
    lastRCSignalTime = millis();
    if (rcSignalLost) {
      rcSignalLost = false;
      Serial.println("RC Signal Recovered!");
      // Potentially transition out of failsafe or wait for re-arm
    }
  }
}

// In loop()
void checkRCSignalFailsafe() {
  if (millis() - lastRCSignalTime > RC_TIMEOUT_MS && !rcSignalLost) {
    rcSignalLost = true;
    Serial.println("RC Signal Lost! Initiating Failsafe...");
    // Trigger failsafe action (e.g., start auto-land sequence)
    initiateFailsafeLand();
  }
}
```

### 2.2. Low Battery Voltage Failsafe

Flying with a critically low battery can lead to sudden power loss and an uncontrolled crash. A low battery failsafe is essential to prevent this.

**Implementation Requirements:**

1.  **Voltage Monitoring:** Implement a voltage divider circuit to measure the drone\'s main battery voltage using an analog input pin on the ESP32. Continuously read and average the battery voltage.
2.  **Thresholds:** Define multiple voltage thresholds:
    *   **Warning Threshold:** (e.g., 3.5V per cell for LiPo). When voltage drops below this, provide a visual warning (e.g., flashing LED, message on TFT display) to the pilot.
    *   **Critical Threshold:** (e.g., 3.3V per cell for LiPo). When voltage drops below this, initiate an automatic landing sequence.
3.  **Failsafe Action:** Upon reaching the critical threshold, the drone should initiate a controlled landing, similar to the RC signal loss failsafe. It should not attempt to return to launch as this might consume too much power.
4.  **Motor Shutdown:** If the voltage drops to an extremely low level (e.g., 3.0V per cell), immediately cut motor power to prevent battery damage.

**Code Snippet (Conceptual for Battery Monitoring):**

```cpp
#define BATTERY_VOLTAGE_PIN 34 // Example analog pin
const float VOLTAGE_DIVIDER_RATIO = 11.0; // Adjust based on your resistor values
const float WARNING_VOLTAGE = 10.5; // For 3S LiPo (3.5V/cell * 3)
const float CRITICAL_VOLTAGE = 9.9; // For 3S LiPo (3.3V/cell * 3)

float currentBatteryVoltage = 0;

void readBatteryVoltage() {
  int rawValue = analogRead(BATTERY_VOLTAGE_PIN);
  // Convert raw ADC reading to voltage
  // ESP32 ADC resolution is 12-bit (0-4095), default Vref is 3.3V
  currentBatteryVoltage = (rawValue / 4095.0) * 3.3 * VOLTAGE_DIVIDER_RATIO;

  if (currentBatteryVoltage < WARNING_VOLTAGE && currentBatteryVoltage > CRITICAL_VOLTAGE) {
    // Display warning on TFT, flash LED
    tft.println("LOW BATTERY!");
  } else if (currentBatteryVoltage < CRITICAL_VOLTAGE) {
    Serial.println("CRITICAL BATTERY! Initiating Failsafe Land...");
    initiateFailsafeLand();
  }
}
```

### 2.3. Sensor Failure Failsafe

While less common, a critical sensor failure (e.g., IMU stops responding, GPS data becomes invalid) can lead to unstable flight. Detecting and responding to these failures is important.

**Implementation Requirements:**

1.  **Sensor Health Monitoring:** Periodically check if sensors are providing valid data. For I2C/SPI sensors, check communication status (e.g., `Wire.endTransmission()` return value, `sensor.begin()` return value). For GPS, check `gps.location.isValid()` and `gps.satellites.value()`.
2.  **Redundancy (if possible):** For critical sensors like the IMU, consider using a secondary sensor or a robust sensor fusion algorithm that can gracefully degrade if one sensor fails.
3.  **Failsafe Action:** If a critical sensor fails during flight, the safest action is usually an immediate controlled landing or, if the drone is very low, a motor shutdown to prevent it from flying away erratically.

### 2.4. Arming/Disarming Safety

To prevent accidental motor spin-up, implement a strict arming and disarming procedure.

**Implementation Requirements:**

1.  **Disarmed State:** Upon power-up, the motors must be disarmed and will not spin regardless of throttle input.
2.  **Arming Sequence:** Require a specific stick combination (e.g., throttle low, yaw right for 2 seconds) or a dedicated switch on the RC transmitter to arm the motors. Provide visual/auditory feedback (e.g., LEDs, buzzer) when armed.
3.  **Disarming Sequence:** Allow disarming via a stick combination (e.g., throttle low, yaw left) or the same dedicated switch. Motors should immediately stop.
4.  **Automatic Disarm:** Automatically disarm motors if:
    *   Throttle is at minimum for a prolonged period (e.g., 5 seconds) while armed but not flying.
    *   The drone is inverted.
    *   A critical failsafe (signal loss, low battery) is triggered.

### 2.5. Pre-Flight Checks

Implement a series of checks that must pass before the drone can be armed.

**Implementation Requirements:**

1.  **Sensor Initialization:** Verify all sensors (IMU, Barometer, GPS) have initialized successfully.
2.  **RC Signal Presence:** Ensure a valid RC signal is being received.
3.  **Battery Voltage:** Check that the battery voltage is above the warning threshold.
4.  **Level Check:** Ensure the drone is relatively level before arming (e.g., roll and pitch angles are within +/- 5 degrees).
5.  **GPS Lock (for GPS modes):** If GPS-dependent flight modes are enabled, ensure a sufficient GPS lock (number of satellites, HDOP) before allowing arming into those modes.

By carefully designing and implementing these failsafe mechanisms, you can significantly enhance the safety and reliability of your DIY quadcopter. The final phase will involve outlining the initial setup, testing, and troubleshooting procedures to get your drone ready for its maiden flight.






## 6. Initial Setup, Testing, and Troubleshooting Procedures




# Initial Setup, Testing, and Troubleshooting Procedures

Bringing your DIY quadcopter to life requires careful initial setup, rigorous testing, and a systematic approach to troubleshooting. This section provides a step-by-step guide to ensure your drone is assembled correctly, its systems are functioning as expected, and you are prepared to address common issues.

## 1. Initial Hardware Assembly and Wiring Verification

Before powering up your drone, a thorough physical inspection and verification of all connections are essential.

1.  **Mechanical Assembly:**
    *   **Frame Assembly:** Assemble the quadcopter frame according to its manufacturer's instructions. Ensure all screws are tightened and components are securely mounted.
    *   **Motor Mounting:** Mount the brushless motors to the arms of the frame. Pay attention to the motor rotation direction for each corner (typically two clockwise and two counter-clockwise). This will be set by propeller direction, but it's good to note for future reference.
    *   **ESC Mounting:** Mount the ESCs to the frame, ideally close to their respective motors to minimize wire length. Ensure good airflow for cooling.
    *   **Flight Controller (ESP32) Mounting:** Mount the ESP32 securely to the center of the frame, using vibration-dampening standoffs if possible. Ensure it is oriented correctly (e.g., arrow pointing forward).
    *   **GY-86 Mounting:** Mount the GY-86 10DOF module as close to the center of gravity as possible, and ensure it is rigidly fixed to the frame. Its orientation is critical for IMU readings.
    *   **GPS Module Mounting:** Mount the NEO-6M GPS module on a mast or elevated platform, away from other electronics (especially ESCs and power lines) to minimize interference. Ensure the ceramic antenna faces upwards with a clear view of the sky.
    *   **RC Receiver Mounting:** Mount the FS-R9B receiver securely, ensuring its antennas are positioned at 90-degree angles to each other and away from carbon fiber or large metal objects for optimal signal reception.
    *   **TFT Display Mounting:** Mount the 1.8 TFT SPI display in a visible location, perhaps on the top plate of the frame, where you can easily read telemetry data during testing.

2.  **Wiring Verification (Double-Check Everything!):**
    *   **Refer to your Wiring Diagram:** Use the detailed wiring diagram you created (or the textual guide provided in the previous section) as your primary reference.
    *   **Power Connections:** Verify all VCC and GND connections. Ensure correct voltage levels (3.3V, 5V) are supplied to each module. Check for any short circuits before applying power.
    *   **I2C Connections (GY-86):** Confirm SDA and SCL lines from GY-86 are connected to the correct ESP32 I2C pins (GPIO 21, 22).
    *   **UART Connections (NEO-6M):** Confirm TX and RX lines from NEO-6M are connected to the correct ESP32 UART pins (GPIO 16, 17).
    *   **SPI Connections (TFT Display):** Verify SCK, MOSI, CS, DC, and RST lines from the TFT display are connected to the correct ESP32 SPI and GPIO pins.
    *   **RC Receiver Connections:** Ensure each channel output from the FS-R9B receiver is connected to the designated ESP32 GPIO pin.
    *   **ESC Signal Connections:** Verify each ESC signal wire is connected to its dedicated PWM-capable GPIO pin on the ESP32. Ensure the ground wire from the ESCs is also connected to the common ground.
    *   **Motor to ESC Connections:** Connect the three motor wires to the three ESC output wires. Do not connect propellers yet!

## 2. Initial Firmware Upload and Basic Functionality Test

With the hardware assembled and wired, it's time to upload the basic flight control firmware and perform initial tests.

1.  **Upload Firmware:**
    *   Connect your ESP32 to your computer via USB.
    *   Open the Arduino IDE and load your main flight control sketch (`main.ino`).
    *   Select the correct ESP32 board (e.g., ESP32 Dev Module) and COM port from `Tools > Board` and `Tools > Port`.
    *   Click the "Upload" button to compile and upload the firmware to the ESP32.
    *   Monitor the Serial Monitor for any errors during upload.

2.  **Serial Monitor Verification:**
    *   After successful upload, open the Serial Monitor (set baud rate to 115200).
    *   You should see initialization messages from your `setup()` function (e.g., "Sensors Initialized", "Setup Complete").
    *   **IMU Data:** Verify that the MPU6050 is providing data. You should see changing raw accelerometer and gyroscope values when you move the drone. If using DMP, you should see stable roll, pitch, and yaw values.
    *   **Barometer Data:** Check for pressure and temperature readings from the MS5611. Altitude should be calculated and displayed.
    *   **GPS Data:** If outdoors with a clear sky view, you should start seeing GPS data (latitude, longitude, altitude, number of satellites) after a few minutes. Look for `gps.location.isValid()` to become true and `gps.satellites.value()` to show a reasonable number (e.g., >4).
    *   **RC Input:** With your FS-TH9X transmitter turned on and bound to the receiver, move the sticks and switches. You should see the corresponding RC channel values (PWM pulse widths) changing in the Serial Monitor.
    *   **TFT Display:** Verify that the TFT display initializes and shows basic information (e.g., "Drone Init..." and then real-time sensor or RC data).

## 3. ESC Calibration

ESCs need to be calibrated to recognize the full range of PWM signals from the ESP32 (typically 1000µs to 2000µs). This ensures that the motors respond correctly to throttle commands.

**Procedure (General, may vary slightly by ESC brand):**

1.  **Disconnect Propellers:** ABSOLUTELY ENSURE ALL PROPELLERS ARE REMOVED FOR SAFETY.
2.  **Power Off Drone:** Disconnect the main battery from the drone.
3.  **Transmitter Setup:** Turn on your FS-TH9X transmitter and set the throttle stick to maximum (full up).
4.  **Connect Main Battery:** While holding the throttle stick at maximum, connect the main battery to the drone. The ESCs should emit a series of beeps (often indicating max throttle detected).
5.  **Move Throttle to Minimum:** Once the beeps stop (or change tone), immediately move the throttle stick to minimum (full down). The ESCs should emit another series of beeps (indicating min throttle detected and calibration complete).
6.  **Test Motors:** After calibration, you should be able to slowly increase the throttle from your transmitter, and the motors should spin up smoothly. Test each motor individually by sending a small PWM signal to its corresponding ESC pin from a test sketch.

## 4. Sensor Calibration (In-Depth)

Perform the detailed sensor calibration procedures as outlined in the previous section (`sensor_calibration_gps_integration.md`).

*   **MPU6050 Accelerometer Calibration:** Follow the 6-point calibration or the simplified level calibration to determine and apply offsets.
*   **MPU6050 Gyroscope Calibration:** Perform the stationary calibration to determine and apply offsets.
*   **HMC5883L Magnetometer Calibration:** Perform the figure-eight calibration to determine hard-iron offsets.
*   **MS5611 Barometer Calibration:** Ensure your code takes an initial ground pressure reading for accurate altitude calculation.

## 5. PID Tuning (Initial Steps)

Before attempting a full flight, perform initial PID tuning steps in a controlled environment.

1.  **Remove Propellers:** Again, ensure propellers are removed.
2.  **Start with Low P-Gains:** Begin with very low `Kp` values for roll, pitch, and yaw (as suggested in `rc_pid_tuning.md`). Keep `Ki` and `Kd` at zero initially.
3.  **Hand Test:** Hold the drone firmly in your hand (without propellers!). Slowly increase throttle until the motors just start to spin. Tilt the drone. You should feel the motors attempting to correct the tilt. If it feels too aggressive or too weak, adjust `Kp`.
4.  **Tethered Test (Optional):** If you have a tethering setup, use it to perform very short, low-altitude hops to observe initial stability.

## 6. First Flight (Caution!)

Only proceed to the first flight after all previous steps are completed and verified. Choose a safe, open outdoor area with no obstacles, people, or animals.

1.  **Pre-Flight Checklist:**
    *   **Propellers Mounted Correctly:** Ensure propellers are mounted in the correct direction for each motor (CW/CCW) and are securely tightened.
    *   **Battery Fully Charged:** Use a fully charged battery.
    *   **RC Transmitter On:** Turn on your transmitter and ensure it's bound to the receiver.
    *   **Clear Area:** Ensure the flight area is clear.
    *   **Failsafe Set:** Confirm your failsafe settings are configured.
    *   **Arming Procedure Known:** Be ready to arm and disarm quickly.
2.  **Arm the Drone:** Perform your defined arming sequence.
3.  **Gentle Takeoff:** Slowly and smoothly increase throttle. The drone should lift off vertically. Do not apply sudden stick inputs.
4.  **Hover and Observe:** Try to maintain a stable hover at a low altitude (e.g., 1-2 meters). Observe the drone's behavior:
    *   Does it drift? (Adjust I-gains)
    *   Does it oscillate? (Adjust P or D gains)
    *   Does it respond correctly to stick inputs?
5.  **Gentle Maneuvers:** Once stable in a hover, try very gentle roll, pitch, and yaw inputs. Observe the response.
6.  **Land Gently:** Slowly reduce throttle to land the drone softly.
7.  **Disarm:** Immediately disarm the motors after landing.

## 7. Troubleshooting Common Issues

Building a DIY drone often involves troubleshooting. Here are some common problems and their potential solutions:

| Issue                               | Possible Cause(s)                                   | Solution(s)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   - [ ] Compile and deliver comprehensive drone build guide

