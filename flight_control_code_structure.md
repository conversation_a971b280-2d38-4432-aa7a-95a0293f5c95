# Flight Control Code Structure for ESP32 Quadcopter

This section outlines the basic software architecture and code structure for the ESP32-based quadcopter. The flight control firmware will be developed using the Arduino IDE environment, leveraging its libraries for ESP32 and various sensors. The code will be modular, with distinct sections for sensor reading, data processing, PID control, motor output, RC input handling, and communication with the GPS and TFT display.

## 1. Development Environment Setup

Before diving into the code, ensure your Arduino IDE is set up for ESP32 development:

1.  **Install ESP32 Board Package:** In Arduino IDE, go to `File > Preferences`, and add `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json` to the 

Additional Boards Manager URLs.` Then, go to `Tools > Board > Boards Manager`, search for "ESP32", and install the "esp32 by Espressif Systems" package.
2.  **Install Libraries:** You will need several libraries for the sensors and display:
    *   **MPU6050/GY-86:** Look for libraries like `Adafruit MPU6050` or `MPU6050_DMP6` (for DMP functionality). You might also need `Adafruit Unified Sensor`.
    *   **HMC5883L:** `Adafruit HMC5883L`.
    *   **MS5611:** `Adafruit MS5611`.
    *   **NEO-6M GPS:** `TinyGPS++` is a popular and robust library for parsing NMEA sentences from GPS modules.
    *   **1.8 TFT SPI:** `Adafruit-ST7735-Library` and `Adafruit-GFX-Library`.
    *   **RC Receiver:** No specific library is typically needed, as you will read PWM signals directly using ESP32's `pulseIn()` function or interrupts.

Install these libraries via `Sketch > Include Library > Manage Libraries...` in the Arduino IDE.

## 2. Overall Code Structure (main.ino)

The main Arduino sketch (`.ino` file) will orchestrate the various functions of the drone. A typical structure includes setup, loop, and various helper functions.

```cpp
// Include necessary libraries
#include <Wire.h> // For I2C communication (GY-86)
#include <SPI.h>  // For SPI communication (TFT Display)
#include <Adafruit_GFX.h> // Core graphics library
#include <Adafruit_ST7735.h> // Hardware-specific library for ST7735
#include <Adafruit_MPU6050.h> // For MPU6050
#include <Adafruit_HMC5883_U.h> // For HMC5883L
#include <Adafruit_MS5611.h> // For MS5611
#include <TinyGPS++.h> // For NEO-6M GPS
#include <HardwareSerial.h> // For ESP32 UART (GPS)

// Define pin assignments (refer to your wiring diagram)
// GY-86 I2C
#define I2C_SDA_PIN 21
#define I2C_SCL_PIN 22

// NEO-6M GPS UART2
#define GPS_RX_PIN 16
#define GPS_TX_PIN 17
HardwareSerial SerialGPS(2); // Use UART2

// 1.8 TFT SPI Display
#define TFT_CS   5
#define TFT_DC   2
#define TFT_RST  4
Adafruit_ST7735 tft = Adafruit_ST7735(TFT_CS, TFT_DC, TFT_RST);

// RC Receiver PWM Input Pins
#define RC_THROTTLE_PIN 14
#define RC_ROLL_PIN     13
#define RC_PITCH_PIN    12
#define RC_YAW_PIN      27
#define RC_MODE_PIN     26 // Flight mode switch

// ESC PWM Output Pins
#define MOTOR_FR_PIN 32 // Front Right
#define MOTOR_RL_PIN 33 // Rear Left
#define MOTOR_FL_PIN 25 // Front Left
#define MOTOR_RR_PIN 26 // Rear Right

// Global variables for sensor data, control values, etc.
// IMU data
float roll, pitch, yaw; // Filtered angles
float accX, accY, accZ; // Accelerometer raw data
float gyroX, gyroY, gyroZ; // Gyroscope raw data
float magX, magY, magZ; // Magnetometer raw data
float temperature, pressure, altitude; // Barometer data

// GPS data
TinyGPSPlus gps;
double latitude, longitude;
float gpsAltitude;
unsigned long chars;
unsigned short sentences, failedChecksum;

// RC Input values (PWM pulse widths)
volatile int rcThrottle, rcRoll, rcPitch, rcYaw, rcMode;

// PID variables
float pidRollOutput, pidPitchOutput, pidYawOutput;

// Motor output values (PWM duty cycles)
int motorFR, motorRL, motorFL, motorRR;

// Function prototypes
void setupSensors();
void readIMU();
void readBarometer();
void readGPS();
void readRCInputs();
void calculatePID();
void updateMotors();
void updateTFTDisplay();
void attachRCInterrupts();

// Interrupt Service Routines (ISRs) for RC input
void IRAM_ATTR rcThrottleISR();
void IRAM_ATTR rcRollISR();
void IRAM_ATTR rcPitchISR();
void IRAM_ATTR rcYawISR();
void IRAM_ATTR rcModeISR();

void setup() {
  Serial.begin(115200);
  SerialGPS.begin(9600, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);

  // Initialize I2C bus
  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);

  setupSensors();
  attachRCInterrupts();

  // Initialize TFT display
  tft.initR(INITR_BLACKTAB); // Initialize ST7735S chip, black tab
  tft.fillScreen(ST77XX_BLACK);
  tft.setCursor(0, 0);
  tft.setTextColor(ST77XX_WHITE);
  tft.setTextWrap(true);
  tft.print("Drone Init...");

  // Setup PWM for motors
  // ESP32 has 16 PWM channels, 0-15
  // Configure PWM frequency and resolution
  ledcSetup(0, 500, 8); // Channel 0 for Motor FR, 500 Hz, 8-bit resolution
  ledcAttachPin(MOTOR_FR_PIN, 0);
  ledcSetup(1, 500, 8); // Channel 1 for Motor RL
  ledcAttachPin(MOTOR_RL_PIN, 1);
  ledcSetup(2, 500, 8); // Channel 2 for Motor FL
  ledcAttachPin(MOTOR_FL_PIN, 2);
  ledcSetup(3, 500, 8); // Channel 3 for Motor RR
  ledcAttachPin(MOTOR_RR_PIN, 3);

  // Initial motor disarm state (e.g., send minimum PWM signal)
  ledcWrite(0, 0); // All motors off
  ledcWrite(1, 0);
  ledcWrite(2, 0);
  ledcWrite(3, 0);

  Serial.println("Setup Complete");
}

void loop() {
  // Main flight control loop
  readIMU();
  readBarometer();
  readGPS(); // Call frequently to process incoming GPS data
  // readRCInputs(); // Handled by interrupts

  // Sensor fusion (e.g., Complementary Filter or Kalman Filter)
  // This will combine accelerometer, gyroscope, and magnetometer data
  // to get accurate roll, pitch, and yaw angles.
  // For simplicity, we'll assume roll, pitch, yaw are updated by IMU reading function for now.

  calculatePID();
  updateMotors();
  updateTFTDisplay();

  // Add a small delay or use a timer to control loop frequency
  // delay(10); // Example, adjust as needed for desired loop rate
}

// --- Function Implementations ---

void setupSensors() {
  // Initialize MPU6050
  // ... (MPU6050 initialization code)

  // Initialize HMC5883L
  // ... (HMC5883L initialization code)

  // Initialize MS5611
  // ... (MS5611 initialization code)

  Serial.println("Sensors Initialized");
}

void readIMU() {
  // Read raw accelerometer, gyroscope, and magnetometer data
  // Apply calibration offsets and scale factors
  // Implement sensor fusion algorithm (e.g., Complementary Filter or Kalman Filter)
  // to calculate stable roll, pitch, and yaw angles.
  // For example:
  // sensors_event_t a, g, mag;
  // mpu.getEvent(&a, &g, &temp);
  // mag.getEvent(&mag);
  // accX = a.acceleration.x; accY = a.acceleration.y; accZ = a.acceleration.z;
  // gyroX = g.gyro.x; gyroY = g.gyro.y; gyroZ = g.gyro.z;
  // magX = mag.magnetic.x; magY = mag.magnetic.y; magZ = mag.magnetic.z;

  // Update roll, pitch, yaw based on sensor fusion
  // roll = ...; pitch = ...; yaw = ...;
}

void readBarometer() {
  // Read pressure and temperature from MS5611
  // Calculate altitude based on pressure
  // For example:
  // pressure = ms5611.readPressure();
  // temperature = ms5611.readTemperature();
  // altitude = ms5611.readAltitude();
}

void readGPS() {
  // While there is data available from the GPS module, process it
  while (SerialGPS.available() > 0) {
    if (gps.encode(SerialGPS.read())) {
      // A new NMEA sentence has been successfully processed
      if (gps.location.isValid()) {
        latitude = gps.location.lat();
        longitude = gps.location.lng();
      }
      if (gps.altitude.isValid()) {
        gpsAltitude = gps.altitude.meters();
      }
      // You can also check for speed, satellites, etc.
    }
  }
  // Optional: Check for new data periodically if not using interrupts
  // if (millis() > lastGpsTime + 1000) { // Update GPS data every 1 second
  //   lastGpsTime = millis();
  //   Serial.print("Satellites: "); Serial.println(gps.satellites.value());
  //   Serial.print("Lat: "); Serial.println(latitude, 6);
  //   Serial.print("Long: "); Serial.println(longitude, 6);
  //   Serial.print("Alt: "); Serial.println(gpsAltitude);
  // }
}

void IRAM_ATTR rcThrottleISR() {
  // Read PWM pulse width for throttle channel
  rcThrottle = pulseIn(RC_THROTTLE_PIN, HIGH, 25000); // Timeout 25ms
}

void IRAM_ATTR rcRollISR() {
  // Read PWM pulse width for roll channel
  rcRoll = pulseIn(RC_ROLL_PIN, HIGH, 25000);
}

void IRAM_ATTR rcPitchISR() {
  // Read PWM pulse width for pitch channel
  rcPitch = pulseIn(RC_PITCH_PIN, HIGH, 25000);
}

void IRAM_ATTR rcYawISR() {
  // Read PWM pulse width for yaw channel
  rcYaw = pulseIn(RC_YAW_PIN, HIGH, 25000);
}

void IRAM_ATTR rcModeISR() {
  // Read PWM pulse width for flight mode channel
  rcMode = pulseIn(RC_MODE_PIN, HIGH, 25000);
}

void attachRCInterrupts() {
  // Attach interrupts to RC input pins
  // RISING and FALLING edges can be used to measure pulse width more accurately
  // For simplicity, using CHANGE and pulseIn() inside ISR for now.
  // Note: pulseIn() inside ISR can be problematic for very fast loops or other interrupts.
  // A better approach is to use RISING and FALLING edge interrupts to record timestamps
  // and calculate pulse width outside the ISR.
  attachInterrupt(digitalPinToInterrupt(RC_THROTTLE_PIN), rcThrottleISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_ROLL_PIN), rcRollISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_PITCH_PIN), rcPitchISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_YAW_PIN), rcYawISR, CHANGE);
  attachInterrupt(digitalPinToInterrupt(RC_MODE_PIN), rcModeISR, CHANGE);
}

void calculatePID() {
  // Implement PID control loops for Roll, Pitch, and Yaw
  // Input: Current roll, pitch, yaw (from sensor fusion) and desired roll, pitch, yaw (from RC input)
  // Output: PID corrections for each axis

  // Example (simplified):
  // float targetRoll = map(rcRoll, 1000, 2000, -30, 30); // Map RC input to desired angle
  // pidRollOutput = Kp_roll * (targetRoll - roll) + Ki_roll * integral_roll + Kd_roll * derivative_roll;

  // Similar for pitch and yaw
}

void updateMotors() {
  // Map PID outputs and throttle to individual motor speeds
  // Ensure motor values are within safe limits (e.g., 1000-2000 for ESCs)
  // Implement motor mixing algorithm for quadcopter (X-configuration or + configuration)

  // Example (simplified for X-configuration):
  // motorFR = rcThrottle + pidPitchOutput - pidRollOutput - pidYawOutput;
  // motorRL = rcThrottle - pidPitchOutput - pidRollOutput + pidYawOutput;
  // motorFL = rcThrottle + pidPitchOutput + pidRollOutput + pidYawOutput;
  // motorRR = rcThrottle - pidPitchOutput + pidRollOutput - pidYawOutput;

  // Constrain motor values to valid PWM range (e.g., 0-255 for 8-bit resolution)
  // ledcWrite(0, motorFR);
  // ledcWrite(1, motorRL);
  // ledcWrite(2, motorFL);
  // ledcWrite(3, motorRR);
}

void updateTFTDisplay() {
  // Display real-time flight data on the 1.8 TFT screen
  // Examples: Roll, Pitch, Yaw, Altitude, GPS coordinates, Battery Voltage, Flight Mode
  // tft.setCursor(0, 0);
  // tft.setTextColor(ST77XX_WHITE, ST77XX_BLACK);
  // tft.print("Roll: "); tft.println(roll);
  // tft.print("Pitch: "); tft.println(pitch);
  // tft.print("Yaw: "); tft.println(yaw);
  // tft.print("Alt: "); tft.println(altitude);
  // tft.print("Lat: "); tft.println(latitude, 4);
  // tft.print("Lon: "); tft.println(longitude, 4);
}
```

## 3. Key Code Modules and Their Responsibilities

### 3.1. Sensor Data Acquisition (GY-86: MPU6050, HMC5883L, MS5611)

This module is responsible for reading raw data from the IMU and barometer. It will involve:

*   **MPU6050:** Reading accelerometer and gyroscope values. The MPU6050 has a Digital Motion Processor (DMP) that can perform on-chip sensor fusion, providing more stable and accurate attitude data (quaternions or Euler angles). Using the DMP is highly recommended to offload processing from the ESP32.
*   **HMC5883L:** Reading magnetometer values. These values will be used to correct yaw drift and provide an absolute heading.
*   **MS5611:** Reading pressure and temperature. Altitude will be calculated from pressure readings.

**Implementation Details:**
*   Use the `Wire.h` library for I2C communication.
*   Initialize each sensor and configure its settings (e.g., data rates, ranges).
*   Implement functions to read raw data and apply any necessary calibration offsets or scale factors.

### 3.2. Sensor Fusion

Raw sensor data from the accelerometer, gyroscope, and magnetometer needs to be combined and filtered to produce accurate and stable orientation estimates (roll, pitch, yaw). Common algorithms include:

*   **Complementary Filter:** A simple and effective filter that combines gyroscope data (good for short-term accuracy) with accelerometer/magnetometer data (good for long-term stability).
*   **Kalman Filter:** A more advanced and computationally intensive filter that provides optimal estimates by considering noise characteristics of each sensor. While more complex, it can yield superior results.

**Implementation Details:**
*   This will be a critical part of the `readIMU()` function or a separate `calculateAttitude()` function.
*   The output of this module will be the `roll`, `pitch`, and `yaw` variables used by the PID controller.

### 3.3. RC Input Processing (FS-TH9X Receiver)

This module handles reading the PWM signals from the RC receiver and converting them into usable control values (e.g., 1000-2000 microsecond pulse widths). Using interrupts for each RC channel is the most efficient way to capture these signals without blocking the main loop.

**Implementation Details:**
*   Attach interrupt service routines (ISRs) to the RC input pins (e.g., using `attachInterrupt()`).
*   Inside the ISRs, use `pulseIn()` to measure the pulse width. **Note:** `pulseIn()` can be blocking and might not be ideal inside an ISR for very fast loops. A more robust method involves using `RISING` and `FALLING` edge interrupts to record timestamps and calculate pulse width in the main loop or a dedicated timer interrupt.
*   Map the raw pulse width values (e.g., 1000-2000) to a normalized range (e.g., -1.0 to 1.0 or -max_angle to +max_angle) for the PID controller.

### 3.4. PID Control

PID (Proportional-Integral-Derivative) controllers are the core of the drone's stabilization system. Separate PID loops will be implemented for roll, pitch, and yaw. An additional PID loop might be used for altitude hold.

*   **Proportional (P):** Responds to the current error (difference between desired and actual angle). Larger error means larger correction.
*   **Integral (I):** Addresses steady-state errors by accumulating past errors. Helps eliminate persistent small errors.
*   **Derivative (D):** Responds to the rate of change of the error. Helps dampen oscillations and improve responsiveness.

**Implementation Details:**
*   Define `Kp`, `Ki`, `Kd` constants for each axis (Roll, Pitch, Yaw).
*   Calculate the error for each axis: `error = desired_angle - current_angle`.
*   Calculate the integral term: `integral += error * dt` (with anti-windup limits).
*   Calculate the derivative term: `derivative = (error - previous_error) / dt`.
*   Calculate PID output: `pid_output = Kp * error + Ki * integral + Kd * derivative`.
*   The `calculatePID()` function will take the current attitude (roll, pitch, yaw) and the desired attitude (from RC input) and produce the `pidRollOutput`, `pidPitchOutput`, `pidYawOutput` values.

### 3.5. Motor Control and Mixing

This module translates the combined throttle input and PID outputs into individual motor speed commands (PWM duty cycles) for each of the four ESCs. The motor mixing algorithm determines how much each motor needs to speed up or slow down to achieve the desired attitude changes.

**Implementation Details:**
*   Use ESP32's `ledc` peripheral for PWM generation. Configure the frequency (e.g., 500 Hz for standard ESCs) and resolution (e.g., 8-bit or 10-bit).
*   Attach PWM channels to the motor control pins using `ledcAttachPin()`.
*   The motor mixing algorithm will combine `rcThrottle` with `pidRollOutput`, `pidPitchOutput`, and `pidYawOutput` to calculate the final `motorFR`, `motorRL`, `motorFL`, `motorRR` values.
*   Ensure motor values are constrained within the valid PWM range for your ESCs (e.g., 1000-2000 microsecond pulse width, or corresponding duty cycle values).

### 3.6. GPS Integration (NEO-6M)

This module is responsible for reading and parsing data from the NEO-6M GPS module. The `TinyGPS++` library simplifies this process.

**Implementation Details:**
*   Initialize a `HardwareSerial` instance for the GPS module (e.g., `SerialGPS.begin(9600, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);`).
*   Continuously read incoming serial data from the GPS and feed it to `gps.encode()`.
*   Periodically check `gps.location.isValid()`, `gps.altitude.isValid()`, etc., to get updated latitude, longitude, and altitude.
*   The GPS data will be used for position hold and potentially for displaying coordinates on the TFT.

### 3.7. TFT Display Management (1.8 TFT SPI)

This module handles displaying real-time flight data and status information on the 1.8-inch TFT screen.

**Implementation Details:**
*   Initialize the `Adafruit_ST7735` and `Adafruit_GFX` libraries.
*   Use `tft.print()`, `tft.println()`, `tft.setCursor()`, `tft.setTextColor()`, etc., to draw text and graphics.
*   Update the display periodically (e.g., every 100-200ms) to show current roll, pitch, yaw, altitude, GPS status, battery voltage, and flight mode.

### 3.8. Failsafe Implementation (Conceptual)

While the detailed failsafe implementation will be covered in a later phase, the code structure should account for it. This involves monitoring critical parameters and taking predefined actions in case of signal loss or critical errors.

**Conceptual Implementation:**
*   Monitor RC signal presence. If signal is lost for a predefined duration, trigger failsafe.
*   Failsafe actions could include:
    *   Landing the drone gently.
    *   Holding current position (if GPS lock is available).
    *   Cutting motor power (as a last resort).

This comprehensive code structure provides a solid foundation for developing the quadcopter's flight control firmware. The next steps will involve implementing the details within each module, starting with sensor calibration and GPS integration.

