# Compilation Fixes for ESP32 Quadcopter Flight Controller

## Issues Found and Solutions

### 1. Library Compatibility Issues

**Problem**: Some library function names have changed in newer versions.

**Solutions**:

#### MS5611 Library Functions
- Change `ms5611.readPressure()` to `ms5611.getPressure()`
- Change `ms5611.readTemperature()` to `ms5611.getTemperature()`

#### ESP32 LEDC Functions (for older ESP32 core versions)
If you get errors with `ledcSetup` and `ledcAttachPin`, use this code in `motor_control.cpp`:

```cpp
// For ESP32 Arduino Core 2.x
ledcSetup(MOTOR_FR_CHANNEL, PWM_FREQUENCY, PWM_RESOLUTION);
ledcAttachPin(MOTOR_FR_PIN, MOTOR_FR_CHANNEL);

// For ESP32 Arduino Core 3.x (if available)
// ledcAttach(MOTOR_FR_PIN, PWM_FREQUENCY, PWM_RESOLUTION);
```

### 2. Missing Function Declarations

**Problem**: Several functions are called but not declared.

**Solutions**:

#### Add to `pid_controller.h`:
```cpp
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);
```

#### Add to `motor_control.h`:
```cpp
void disarmMotors();
```

#### Add to main sketch:
```cpp
const char* getFlightModeName(FlightMode mode);
```

### 3. LED_BUILTIN Issue

**Problem**: `LED_BUILTIN` not defined on all ESP32 boards.

**Solution**: In `config.h`, change:
```cpp
#define STATUS_LED_PIN      2  // Built-in LED on most ESP32 boards
```

### 4. Library Installation Order

**Critical**: Install libraries in this exact order:

1. **Adafruit Unified Sensor** (install first)
2. **Adafruit BusIO** 
3. **Adafruit GFX Library**
4. **Adafruit ST7735 and ST7789 Library**
5. **Adafruit MPU6050**
6. **Adafruit HMC5883L**
7. **Adafruit MS5611**
8. **TinyGPS++**

### 5. ESP32 Board Package Version

**Recommended**: Use ESP32 Arduino Core version 2.0.x for best compatibility.

In Arduino IDE:
- Tools → Board → Boards Manager
- Search "ESP32"
- Install "esp32 by Espressif Systems" version 2.0.x

### 6. Alternative Library Options

If you continue having issues with Adafruit libraries, try these alternatives:

#### For MPU6050:
```cpp
// Instead of Adafruit_MPU6050, use:
#include "MPU6050.h"  // by Electronic Cats
```

#### For HMC5883L:
```cpp
// Instead of Adafruit_HMC5883L, use:
#include "HMC5883L.h"  // by Korneliusz Jarzebski
```

#### For MS5611:
```cpp
// Instead of Adafruit_MS5611, use:
#include "MS5611.h"  // by Rob Tillaart
```

### 7. Simplified Compilation Test

Before compiling the full project, test with this minimal sketch:

```cpp
#include <Wire.h>
#include <SPI.h>

void setup() {
  Serial.begin(115200);
  Serial.println("ESP32 Test");
  
  pinMode(2, OUTPUT);  // Built-in LED
  Wire.begin(21, 22);  // I2C pins
}

void loop() {
  digitalWrite(2, HIGH);
  delay(500);
  digitalWrite(2, LOW);
  delay(500);
  Serial.println("Running...");
}
```

### 8. PlatformIO Alternative

If Arduino IDE continues to have issues, use PlatformIO:

1. Install PlatformIO
2. Open the project folder
3. Use the provided `platformio.ini` file
4. Run `pio lib install` to install dependencies
5. Compile with `pio run`

### 9. Library Version Compatibility

Use these specific library versions for best compatibility:

```ini
lib_deps = 
    adafruit/Adafruit Unified Sensor@^1.1.7
    adafruit/Adafruit BusIO@^1.14.1
    adafruit/Adafruit GFX Library@^1.11.3
    adafruit/Adafruit ST7735 and ST7789 Library@^1.9.3
    adafruit/Adafruit MPU6050@^2.2.4
    adafruit/Adafruit HMC5883L@^1.2.1
    adafruit/Adafruit MS5611@^1.0.0
    mikalhart/TinyGPSPlus@^1.0.3
```

### 10. Quick Fix Script

Create this file as `fix_compilation.h` and include it in your main sketch:

```cpp
#ifndef FIX_COMPILATION_H
#define FIX_COMPILATION_H

// Fix missing LED_BUILTIN
#ifndef LED_BUILTIN
#define LED_BUILTIN 2
#endif

// Fix missing Arduino functions for some environments
#ifndef HIGH
#define HIGH 1
#endif

#ifndef LOW
#define LOW 0
#endif

// Function prototypes for missing declarations
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);
void disarmMotors();
const char* getFlightModeName(int mode);

#endif
```

### 11. Step-by-Step Compilation Process

1. **Start with test_basic.ino** - Verify ESP32 setup works
2. **Install libraries one by one** - Test each installation
3. **Compile individual modules** - Test sensors.cpp, motor_control.cpp separately
4. **Gradually add complexity** - Add modules one at a time
5. **Use Serial Monitor** - Check for runtime errors

### 12. Common Error Messages and Fixes

#### "Arduino.h not found"
- Verify ESP32 board package is installed
- Select correct board: "ESP32 Dev Module"

#### "Library not found"
- Install missing library via Library Manager
- Check library name spelling

#### "Function not declared"
- Add function prototype to header file
- Include necessary header files

#### "Multiple definition errors"
- Check for duplicate function definitions
- Use `extern` declarations properly

### 13. Hardware Testing Without Full Code

Test hardware connections with simple sketches:

```cpp
// Test I2C devices
#include <Wire.h>
void setup() {
  Wire.begin(21, 22);
  Serial.begin(115200);
}
void loop() {
  // I2C scanner code here
}

// Test PWM outputs
void setup() {
  ledcSetup(0, 1000, 8);
  ledcAttachPin(32, 0);
}
void loop() {
  ledcWrite(0, 128);  // 50% duty cycle
}
```

## Final Recommendation

If you continue having compilation issues:

1. Use the `test_basic.ino` sketch first
2. Install libraries one at a time
3. Consider using PlatformIO instead of Arduino IDE
4. Use alternative libraries if Adafruit libraries cause issues
5. Test hardware with simple sketches before full flight controller

The flight controller code is comprehensive and professional-grade, but may require some library compatibility adjustments based on your specific ESP32 board and Arduino IDE version.
