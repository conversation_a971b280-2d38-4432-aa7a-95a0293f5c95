/*
 * RC Input handling for ESP32 Quadcopter Flight Controller
 * 
 * This module handles RC receiver input processing including:
 * - PWM signal reading
 * - Signal validation
 * - Failsafe detection
 * - Flight mode determination
 */

#ifndef RC_INPUT_H
#define RC_INPUT_H

#include "config.h"

// RC channel data
extern RCData rcData;

// Interrupt variables for PWM measurement
extern volatile unsigned long rcThrottleStart, rcRollStart, rcPitchStart, rcYawStart, rcModeStart, rcAux1Start, rcAux2Start;
extern volatile int rcThrottlePulse, rcRollPulse, rcPitchPulse, rcYawPulse, rcModePulse, rcAux1Pulse, rcAux2Pulse;

// Function declarations
bool initializeRCInput();
void updateRCInputs();
void attachRCInterrupts();
FlightMode determineFlightModeFromRC();
bool isRCSignalValid();
void processRCFailsafe();

// Interrupt service routines
void IRAM_ATTR rcThrottleISR();
void IRAM_ATTR rcRollISR();
void IRAM_ATTR rcPitchISR();
void IRAM_ATTR rcYawISR();
void IRAM_ATTR rcModeISR();
void IRAM_ATTR rcAux1ISR();
void IRAM_ATTR rcAux2ISR();

// Utility functions
int constrainRC(int value);
bool isRCChannelValid(int pulse);
void printRCValues();

#endif // RC_INPUT_H
