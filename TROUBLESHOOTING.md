# Troubleshooting Guide

This guide helps diagnose and resolve common issues with the ESP32 Quadcopter Flight Controller.

## General Diagnostic Steps

### 1. Check Serial Monitor Output
- Open Serial Monitor at 115200 baud
- Look for initialization messages
- Check for error codes or warnings
- Monitor real-time sensor data

### 2. Verify Power Supply
- Check battery voltage (should be >10.5V for 3S)
- Verify 3.3V and 5V rails are stable
- Ensure adequate current capacity
- Check for voltage drops under load

### 3. Test Individual Components
- Verify each sensor responds individually
- Test RC receiver signal quality
- Check motor/ESC functionality
- Validate display operation

## Common Issues and Solutions

### System Won't Start

**Symptoms:**
- No serial output
- Display remains blank
- LED indicators don't work

**Possible Causes & Solutions:**

1. **Power Issues**
   - Check battery connection and voltage
   - Verify power distribution board functionality
   - Test ESP32 power supply (3.3V)
   - Solution: Fix power connections, replace faulty components

2. **Firmware Upload Problems**
   - Verify correct board selection in Arduino IDE
   - Check USB cable and connection
   - Try different upload speed (115200)
   - Solution: Re-upload firmware with correct settings

3. **Hardware Failure**
   - Test ESP32 with simple blink sketch
   - Check for damaged components
   - Verify all connections are secure
   - Solution: Replace faulty hardware

### Sensors Not Detected

**Symptoms:**
- "Sensor initialization failed" messages
- Missing sensor data on display
- Calibration fails

**I2C Sensor Issues (GY-86):**

1. **Connection Problems**
   - Verify SDA (GPIO21) and SCL (GPIO22) connections
   - Check power supply to sensor module
   - Ensure common ground connection
   - Solution: Fix wiring, check continuity

2. **I2C Communication Errors**
   - Add 4.7kΩ pull-up resistors on SDA/SCL lines
   - Reduce I2C clock speed (100kHz)
   - Check for multiple devices with same address
   - Solution: Add pull-ups, modify I2C settings

3. **Sensor Module Failure**
   - Test with I2C scanner sketch
   - Try different sensor module
   - Check for physical damage
   - Solution: Replace sensor module

**GPS Issues (NEO-6M):**

1. **No GPS Fix**
   - Move to open area with clear sky view
   - Wait longer for initial fix (cold start can take 15+ minutes)
   - Check antenna connection and placement
   - Solution: Improve antenna placement, be patient

2. **UART Communication Problems**
   - Verify RX (GPIO16) and TX (GPIO17) connections
   - Check baud rate setting (9600)
   - Ensure correct voltage levels (3.3V/5V)
   - Solution: Fix UART wiring, verify settings

### RC Control Issues

**Symptoms:**
- "RC signal lost" warnings
- Erratic control response
- Motors won't arm

**Signal Reception Problems:**

1. **Receiver Binding**
   - Re-bind receiver to transmitter
   - Check receiver LED status indicators
   - Verify correct model selection on transmitter
   - Solution: Follow binding procedure

2. **Power Supply Issues**
   - Ensure receiver has stable 5V supply
   - Check for voltage drops during operation
   - Verify adequate current capacity
   - Solution: Improve power distribution

3. **Interference**
   - Move away from WiFi routers, cell towers
   - Check for loose connections causing noise
   - Ensure proper antenna orientation
   - Solution: Relocate, fix connections

**PWM Signal Issues:**

1. **Incorrect Pin Mapping**
   - Verify RC channels connected to correct GPIO pins
   - Check pin definitions in config.h
   - Test with multimeter or oscilloscope
   - Solution: Correct wiring, update pin definitions

2. **Signal Range Problems**
   - Calibrate RC transmitter endpoints
   - Adjust EPA (End Point Adjustment) settings
   - Check for reversed channels
   - Solution: Recalibrate transmitter and receiver

### Motor/ESC Problems

**Symptoms:**
- Motors don't respond
- Uneven motor speeds
- Motors spin in wrong direction

**ESC Issues:**

1. **ESC Calibration**
   - Perform ESC calibration procedure
   - Ensure all ESCs use same firmware/settings
   - Check PWM signal range (1000-2000µs)
   - Solution: Calibrate ESCs, verify settings

2. **Power Distribution**
   - Check battery connections to ESCs
   - Verify adequate wire gauge for current
   - Test individual ESC functionality
   - Solution: Improve power distribution

3. **Signal Problems**
   - Verify PWM output pins (GPIO32, 15, 19, 0)
   - Check signal ground connections
   - Test PWM frequency (500Hz typical)
   - Solution: Fix signal wiring, verify PWM settings

**Motor Direction:**

1. **Incorrect Rotation**
   - Swap any two motor wires to reverse direction
   - Verify motor layout matches code configuration
   - Check propeller installation direction
   - Solution: Correct motor wiring/prop installation

### Flight Stability Issues

**Symptoms:**
- Oscillations during flight
- Drone drifts or won't hold position
- Uncontrollable flight behavior

**PID Tuning Problems:**

1. **Oscillations**
   - Reduce P gain values
   - Increase D gain to dampen oscillations
   - Check for mechanical vibrations
   - Solution: Retune PID parameters

2. **Sluggish Response**
   - Increase P gain values
   - Check for excessive filtering
   - Verify sensor mounting is rigid
   - Solution: Increase gains, improve mounting

3. **Steady-State Errors**
   - Increase I gain values
   - Check for sensor bias/drift
   - Verify calibration accuracy
   - Solution: Tune I gain, recalibrate sensors

**Sensor Issues:**

1. **Vibration Problems**
   - Use vibration dampening for sensor module
   - Check propeller balance
   - Verify motor mounting is secure
   - Solution: Improve vibration isolation

2. **Calibration Errors**
   - Recalibrate all sensors
   - Check for magnetic interference (magnetometer)
   - Verify level surface for calibration
   - Solution: Proper calibration procedure

### Display Issues

**Symptoms:**
- Blank or corrupted display
- Missing or incorrect data
- Display freezes

**SPI Communication:**

1. **Wiring Problems**
   - Verify SPI connections (CS, DC, RST, MOSI, SCLK)
   - Check power supply to display (3.3V)
   - Ensure proper ground connection
   - Solution: Fix SPI wiring

2. **Display Driver Issues**
   - Verify correct display driver (ST7735 vs ST7789)
   - Check initialization parameters
   - Try different SPI speed settings
   - Solution: Update driver settings

### GPS Navigation Issues

**Symptoms:**
- Position hold doesn't work
- Return-to-launch fails
- GPS coordinates incorrect

**GPS Accuracy:**

1. **Poor Fix Quality**
   - Wait for more satellites (6+ recommended)
   - Check HDOP value (<2.0 preferred)
   - Improve antenna placement
   - Solution: Better GPS conditions

2. **Position Drift**
   - Check for GPS multipath interference
   - Verify coordinate system settings
   - Calibrate position PID controllers
   - Solution: Improve GPS environment, tune PIDs

### Failsafe Issues

**Symptoms:**
- Failsafe triggers unexpectedly
- Failsafe doesn't activate when needed
- Incorrect failsafe behavior

**Configuration Problems:**

1. **Threshold Settings**
   - Adjust RC signal timeout values
   - Check battery voltage thresholds
   - Verify GPS requirements for modes
   - Solution: Tune failsafe parameters

2. **Sensor Monitoring**
   - Check sensor health monitoring
   - Verify failsafe action priorities
   - Test manual failsafe trigger
   - Solution: Improve sensor monitoring

## Diagnostic Tools

### Built-in Diagnostics

1. **Serial Monitor Commands**
   - `status` - System status report
   - `sensors` - Raw sensor data
   - `rc` - RC channel values
   - `motors` - Motor output values
   - `gps` - GPS status and data

2. **Display Pages**
   - Flight data page
   - GPS status page
   - System status page
   - RC input page

### External Tools

1. **Multimeter**
   - Voltage measurements
   - Continuity testing
   - Current consumption

2. **Oscilloscope**
   - PWM signal analysis
   - I2C/SPI communication
   - Noise investigation

3. **Logic Analyzer**
   - Digital protocol analysis
   - Timing verification
   - Bus troubleshooting

## Emergency Procedures

### In-Flight Emergencies

1. **Loss of Control**
   - Switch to manual mode immediately
   - Reduce throttle gradually
   - Attempt controlled landing
   - Use emergency stop if necessary

2. **Failsafe Activation**
   - Don't panic - let system handle it
   - Be ready to take manual control
   - Monitor descent/landing
   - Investigate cause after landing

3. **System Malfunction**
   - Cut throttle immediately
   - Switch off transmitter if safe
   - Clear area of people/property
   - Assess damage after landing

### Ground Emergencies

1. **Smoke/Fire**
   - Disconnect battery immediately
   - Move away from aircraft
   - Use appropriate fire extinguisher
   - Ventilate area

2. **Runaway Motors**
   - Disconnect battery power
   - Stay clear of propellers
   - Check for control system failure
   - Investigate wiring issues

## Getting Help

### Before Asking for Help

1. **Gather Information**
   - Serial monitor output
   - System configuration details
   - Steps to reproduce issue
   - Photos/videos of problem

2. **Try Basic Solutions**
   - Power cycle system
   - Check all connections
   - Verify configuration
   - Test with minimal setup

### Support Resources

1. **Documentation**
   - README.md - General information
   - WIRING.md - Connection details
   - CALIBRATION.md - Sensor setup
   - This troubleshooting guide

2. **Community Support**
   - GitHub issues page
   - Arduino/ESP32 forums
   - RC/drone communities
   - Local maker spaces

3. **Professional Help**
   - Electronics repair shops
   - RC hobby stores
   - Engineering consultants
   - Educational institutions
