# ESP32 Quadcopter Flight Controller

A comprehensive flight control system for ESP32-based quadcopters featuring advanced sensor fusion, GPS navigation, and multiple flight modes.

## Features

- **10DOF IMU Integration**: MPU6050 (accelerometer/gyroscope) + HMC5883L (magnetometer) + MS5611 (barometer)
- **GPS Navigation**: NEO-6M GPS module for position hold and return-to-launch
- **Multiple Flight Modes**: Acro, Stabilize, Altitude Hold, Position Hold, Return-to-Launch
- **RC Control**: FS-TH9X transmitter/receiver support
- **Real-time Display**: 1.8" TFT SPI display for flight data
- **Advanced PID Control**: Tunable PID controllers for all axes
- **Comprehensive Failsafe**: Multiple safety mechanisms
- **Modular Design**: Clean, maintainable code structure

## Hardware Requirements

### Core Components
- **ESP32 Development Board** (38-pin recommended)
- **GY-86 10DOF Module** (MPU6050 + HMC5883L + MS5611)
- **NEO-6M GPS Module**
- **1.8" TFT SPI Display** (ST7735 driver)
- **FS-TH9X RC Transmitter/Receiver**
- **4x Brushless Motors** (2204-2300KV recommended)
- **4x ESCs** (20A-30A)
- **Quadcopter Frame** (250mm-450mm)
- **LiPo Battery** (3S 2200mAh recommended)

### Optional Components
- **Buzzer** for audio alerts
- **LED strips** for status indication
- **Voltage divider** for battery monitoring

## Pin Connections

### I2C (GY-86 Sensor Module)
- SDA: GPIO 21
- SCL: GPIO 22
- VCC: 3.3V
- GND: GND

### GPS (NEO-6M)
- RX: GPIO 16
- TX: GPIO 17
- VCC: 3.3V
- GND: GND

### TFT Display (1.8" SPI)
- CS: GPIO 5
- DC: GPIO 2
- RST: GPIO 4
- MOSI: GPIO 23
- SCLK: GPIO 18
- VCC: 3.3V
- GND: GND

### RC Receiver (PWM)
- Throttle: GPIO 14
- Roll: GPIO 13
- Pitch: GPIO 12
- Yaw: GPIO 27
- Mode: GPIO 26
- Aux1: GPIO 25
- Aux2: GPIO 33

### Motors (ESC PWM)
- Front Right: GPIO 32
- Front Left: GPIO 15
- Rear Right: GPIO 19
- Rear Left: GPIO 0

### Additional
- Status LED: Built-in LED
- Buzzer: GPIO 35
- Battery Monitor: GPIO 34

## Software Setup

### 1. Arduino IDE Setup
1. Install Arduino IDE (1.8.19 or later)
2. Add ESP32 board package:
   - File → Preferences → Additional Boards Manager URLs
   - Add: `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json`
   - Tools → Board → Boards Manager → Search "ESP32" → Install

### 2. Library Installation
Install the following libraries via Library Manager:
- Adafruit GFX Library
- Adafruit ST7735 and ST7789 Library
- Adafruit MPU6050
- Adafruit HMC5883L
- Adafruit MS5611
- Adafruit Unified Sensor
- TinyGPS++

### 3. Board Configuration
- Board: "ESP32 Dev Module"
- Upload Speed: 921600
- CPU Frequency: 240MHz
- Flash Frequency: 80MHz
- Flash Mode: QIO
- Flash Size: 4MB
- Partition Scheme: Default 4MB

## Quick Start

### 1. Hardware Assembly
1. Mount all components on the quadcopter frame
2. Connect components according to pin diagram
3. Ensure proper power distribution
4. Balance propellers and check motor rotation

### 2. Initial Setup
1. Upload the firmware to ESP32
2. Open Serial Monitor (115200 baud)
3. Follow calibration prompts
4. Configure RC transmitter
5. Test all systems before flight

### 3. Calibration
1. **Accelerometer**: Keep drone level and still
2. **Gyroscope**: Keep drone absolutely motionless
3. **Magnetometer**: Rotate drone in figure-8 pattern
4. **Barometer**: Automatic ground pressure reference
5. **RC**: Center all sticks and switches

### 4. First Flight
1. Ensure all safety checks pass
2. Start in Stabilize mode
3. Arm motors with throttle down + yaw right
4. Gradually increase throttle
5. Test basic controls at low altitude

## Flight Modes

### Acro Mode
- Direct rate control
- No self-leveling
- For experienced pilots

### Stabilize Mode  
- Angle control with self-leveling
- Returns to level when sticks centered
- Recommended for beginners

### Altitude Hold
- Maintains current altitude
- Throttle controls climb/descent rate
- Uses barometer feedback

### Position Hold
- Maintains GPS position
- Requires good GPS fix
- Automatic drift correction

### Return to Launch
- Autonomous return to takeoff point
- Requires GPS and home position set
- Automatic landing sequence

## Safety Features

### Failsafe Mechanisms
- **RC Signal Loss**: Automatic landing or RTL
- **Low Battery**: Forced landing
- **GPS Loss**: Mode downgrade to stabilize
- **Sensor Failure**: Emergency disarm
- **Geofence**: Boundary enforcement (configurable)

### Pre-flight Checks
- Sensor calibration status
- GPS fix quality
- Battery voltage
- RC signal strength
- Motor/ESC functionality

## Tuning Guide

### PID Tuning Order
1. **Roll/Pitch P-gain**: Start low, increase until oscillation
2. **Roll/Pitch D-gain**: Add to dampen oscillations  
3. **Roll/Pitch I-gain**: Eliminate steady-state error
4. **Yaw PID**: Usually requires different values
5. **Altitude PID**: For altitude hold mode

### Tuning Tips
- Always tune in safe, open area
- Start with conservative values
- Make small incremental changes
- Test thoroughly between changes
- Keep backup of working values

## Troubleshooting

### Common Issues
- **Motors won't arm**: Check calibration, RC signal, battery
- **Unstable flight**: Tune PID values, check sensor mounting
- **GPS issues**: Check antenna placement, wait for fix
- **Display problems**: Verify SPI connections
- **RC signal loss**: Check transmitter/receiver binding

### Debug Tools
- Serial monitor output
- TFT display status pages
- Telemetry data logging
- Built-in diagnostics

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Test thoroughly
4. Submit a pull request

## License

This project is open source and available under the MIT License.

## Disclaimer

**IMPORTANT SAFETY NOTICE**: This is experimental flight control software. Always:
- Test thoroughly in safe environments
- Follow local regulations
- Use appropriate safety equipment
- Never fly over people or property
- Maintain visual line of sight
- Have emergency procedures ready

The authors are not responsible for any damage or injury resulting from use of this software.

## Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Check the troubleshooting guide
- Review the documentation
- Join the community discussions
