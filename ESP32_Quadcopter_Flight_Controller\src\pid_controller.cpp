/*
 * PID Controller implementation
 */

#include "pid_controller.h"

// PID controllers
PIDController rollPID;
PIDController pitchPID;
PIDController yawPID;
PIDController altitudePID;
PIDController positionXPID;
PIDController positionYPID;

// Setpoints
float rollSetpoint = 0;
float pitchSetpoint = 0;
float yawSetpoint = 0;
float altitudeSetpoint = 0;
float positionXSetpoint = 0;
float positionYSetpoint = 0;

void initializePIDControllers() {
  // Initialize Roll PID
  setPIDGains(&rollPID, ROLL_KP, ROLL_KI, ROLL_KD);
  setPIDLimits(&rollPID, PID_INTEGRAL_LIMIT, PID_OUTPUT_LIMIT);
  
  // Initialize Pitch PID
  setPIDGains(&pitchPID, PITCH_KP, PITCH_KI, PITCH_KD);
  setPIDLimits(&pitchPID, PID_INTEGRAL_LIMIT, PID_OUTPUT_LIMIT);
  
  // Initialize Yaw PID
  setPIDGains(&yawPID, YA<PERSON>_KP, YAW_KI, YAW_KD);
  setPIDLimits(&yawPID, PID_INTEGRAL_LIMIT, PID_OUTPUT_LIMIT);
  
  // Initialize Altitude PID
  setPIDGains(&altitudePID, ALT_KP, ALT_KI, ALT_KD);
  setPIDLimits(&altitudePID, PID_INTEGRAL_LIMIT, PID_OUTPUT_LIMIT);
  
  // Initialize Position PIDs
  setPIDGains(&positionXPID, POS_KP, POS_KI, POS_KD);
  setPIDLimits(&positionXPID, PID_INTEGRAL_LIMIT, PID_OUTPUT_LIMIT);
  
  setPIDGains(&positionYPID, POS_KP, POS_KI, POS_KD);
  setPIDLimits(&positionYPID, PID_INTEGRAL_LIMIT, PID_OUTPUT_LIMIT);
  
  Serial.println("PID controllers initialized");
}

void calculatePIDOutputs(float deltaTime) {
  extern FlightMode currentFlightMode;
  
  switch (currentFlightMode) {
    case ACRO_MODE:
      calculateAcroPID(deltaTime);
      break;
      
    case STABILIZE_MODE:
      calculateStabilizePID(deltaTime);
      break;
      
    case ALTITUDE_HOLD_MODE:
      calculateStabilizePID(deltaTime);
      calculateAltitudeHoldPID(deltaTime);
      break;
      
    case POSITION_HOLD_MODE:
      calculateStabilizePID(deltaTime);
      calculateAltitudeHoldPID(deltaTime);
      calculatePositionHoldPID(deltaTime);
      break;
      
    default:
      calculateStabilizePID(deltaTime);
      break;
  }
}

void calculateStabilizePID(float deltaTime) {
  extern SystemState systemState;
  extern RCData rcData;
  
  // Convert RC inputs to angle setpoints
  rollSetpoint = mapFloat(rcData.roll, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_ANGLE, MAX_ANGLE);
  pitchSetpoint = mapFloat(rcData.pitch, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_ANGLE, MAX_ANGLE);
  yawSetpoint = mapFloat(rcData.yaw, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_YAW_RATE, MAX_YAW_RATE);
  
  // Calculate PID outputs
  rollPID.output = calculatePID(&rollPID, rollSetpoint, systemState.roll, deltaTime);
  pitchPID.output = calculatePID(&pitchPID, pitchSetpoint, systemState.pitch, deltaTime);
  yawPID.output = calculatePID(&yawPID, yawSetpoint, systemState.yawRate, deltaTime);
}

void calculateAcroPID(float deltaTime) {
  extern SystemState systemState;
  extern RCData rcData;
  
  // Convert RC inputs to rate setpoints
  float rollRateSetpoint = mapFloat(rcData.roll, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_YAW_RATE, MAX_YAW_RATE);
  float pitchRateSetpoint = mapFloat(rcData.pitch, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_YAW_RATE, MAX_YAW_RATE);
  float yawRateSetpoint = mapFloat(rcData.yaw, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_YAW_RATE, MAX_YAW_RATE);
  
  // Calculate PID outputs based on rates
  rollPID.output = calculatePID(&rollPID, rollRateSetpoint, systemState.rollRate, deltaTime);
  pitchPID.output = calculatePID(&pitchPID, pitchRateSetpoint, systemState.pitchRate, deltaTime);
  yawPID.output = calculatePID(&yawPID, yawRateSetpoint, systemState.yawRate, deltaTime);
}

void calculateAltitudeHoldPID(float deltaTime) {
  extern SystemState systemState;
  extern RCData rcData;
  
  // If throttle is near center, hold current altitude
  if (abs(rcData.throttle - RC_MID_PULSE) < RC_DEADBAND) {
    // Hold current altitude
    if (altitudeSetpoint == 0) {
      altitudeSetpoint = systemState.altitude;
    }
  } else {
    // Adjust altitude setpoint based on throttle input
    float climbRate = mapFloat(rcData.throttle, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_CLIMB_RATE, MAX_CLIMB_RATE);
    altitudeSetpoint += climbRate * deltaTime;
  }
  
  // Calculate altitude PID output
  altitudePID.output = calculatePID(&altitudePID, altitudeSetpoint, systemState.altitude, deltaTime);
}

void calculatePositionHoldPID(float deltaTime) {
  extern SystemState systemState;
  extern RCData rcData;
  
  // If roll/pitch sticks are centered, hold position
  if (abs(rcData.roll - RC_MID_PULSE) < RC_DEADBAND && 
      abs(rcData.pitch - RC_MID_PULSE) < RC_DEADBAND) {
    
    // Set position setpoints if not already set
    if (positionXSetpoint == 0 && positionYSetpoint == 0) {
      positionXSetpoint = systemState.gpsData.latitude;
      positionYSetpoint = systemState.gpsData.longitude;
    }
    
    // Calculate position errors (simplified - should use proper distance calculation)
    float posErrorX = (positionXSetpoint - systemState.gpsData.latitude) * 111320.0; // meters
    float posErrorY = (positionYSetpoint - systemState.gpsData.longitude) * 111320.0 * cos(systemState.gpsData.latitude * PI / 180.0);
    
    // Calculate position PID outputs
    positionXPID.output = calculatePID(&positionXPID, 0, posErrorX, deltaTime);
    positionYPID.output = calculatePID(&positionYPID, 0, posErrorY, deltaTime);
    
    // Convert position corrections to angle setpoints
    rollSetpoint = constrainFloat(positionYPID.output, -MAX_ANGLE, MAX_ANGLE);
    pitchSetpoint = constrainFloat(-positionXPID.output, -MAX_ANGLE, MAX_ANGLE);
  } else {
    // Manual control - reset position setpoints
    positionXSetpoint = 0;
    positionYSetpoint = 0;
    
    // Use normal stabilize mode
    rollSetpoint = mapFloat(rcData.roll, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_ANGLE, MAX_ANGLE);
    pitchSetpoint = mapFloat(rcData.pitch, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_ANGLE, MAX_ANGLE);
  }
  
  // Always calculate yaw normally
  yawSetpoint = mapFloat(rcData.yaw, RC_MIN_PULSE, RC_MAX_PULSE, -MAX_YAW_RATE, MAX_YAW_RATE);
  
  // Calculate attitude PID outputs
  rollPID.output = calculatePID(&rollPID, rollSetpoint, systemState.roll, deltaTime);
  pitchPID.output = calculatePID(&pitchPID, pitchSetpoint, systemState.pitch, deltaTime);
  yawPID.output = calculatePID(&yawPID, yawSetpoint, systemState.yawRate, deltaTime);
}

float calculatePID(PIDController* pid, float setpoint, float input, float deltaTime) {
  // Calculate error
  pid->error = setpoint - input;
  
  // Calculate integral (with windup protection)
  pid->integral += pid->error * deltaTime;
  pid->integral = constrainFloat(pid->integral, -pid->integralLimit, pid->integralLimit);
  
  // Calculate derivative
  pid->derivative = (pid->error - pid->previousError) / deltaTime;
  
  // Calculate PID output
  float output = (pid->kp * pid->error) + (pid->ki * pid->integral) + (pid->kd * pid->derivative);
  
  // Constrain output
  output = constrainFloat(output, -pid->outputLimit, pid->outputLimit);
  
  // Store error for next iteration
  pid->previousError = pid->error;
  
  return output;
}

void resetPIDIntegrators() {
  rollPID.integral = 0;
  pitchPID.integral = 0;
  yawPID.integral = 0;
  altitudePID.integral = 0;
  positionXPID.integral = 0;
  positionYPID.integral = 0;
  
  rollPID.previousError = 0;
  pitchPID.previousError = 0;
  yawPID.previousError = 0;
  altitudePID.previousError = 0;
  positionXPID.previousError = 0;
  positionYPID.previousError = 0;
}

void setPIDGains(PIDController* pid, float kp, float ki, float kd) {
  pid->kp = kp;
  pid->ki = ki;
  pid->kd = kd;
}

void setPIDLimits(PIDController* pid, float integralLimit, float outputLimit) {
  pid->integralLimit = integralLimit;
  pid->outputLimit = outputLimit;
}

float constrainFloat(float value, float min, float max) {
  if (value < min) return min;
  if (value > max) return max;
  return value;
}

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}
