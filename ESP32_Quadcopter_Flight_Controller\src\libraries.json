{"name": "ESP32 Quadcopter Flight Controller", "version": "1.0.0", "description": "Comprehensive flight control system for ESP32-based quadcopters", "keywords": ["esp32", "quadcopter", "drone", "flight-controller", "a<PERSON><PERSON><PERSON>"], "repository": {"type": "git", "url": "https://github.com/your-username/esp32-quadcopter-flight-controller"}, "authors": [{"name": "ESP32 Quadcopter Project", "email": "<EMAIL>"}], "license": "MIT", "frameworks": ["a<PERSON><PERSON><PERSON>"], "platforms": ["espressif32"], "dependencies": {"core_libraries": {"Wire": {"version": ">=1.0.0", "description": "I2C communication library for sensor modules", "required": true, "included_with": "Arduino Core"}, "SPI": {"version": ">=1.0.0", "description": "SPI communication library for TFT display", "required": true, "included_with": "Arduino Core"}, "HardwareSerial": {"version": ">=1.0.0", "description": "UART communication library for GPS module", "required": true, "included_with": "ESP32 Core"}}, "external_libraries": {"Adafruit_GFX": {"name": "Adafruit GFX Library", "version": ">=1.11.3", "author": "Adafruit", "description": "Core graphics library for displays", "required": true, "install_command": "Library Manager: 'Adafruit GFX Library'", "github": "https://github.com/adafruit/Adafruit-GFX-Library", "dependencies": []}, "Adafruit_ST7735": {"name": "Adafruit ST7735 and ST7789 Library", "version": ">=1.9.3", "author": "Adafruit", "description": "Hardware-specific library for ST7735 TFT displays", "required": true, "install_command": "Library Manager: 'Adafruit ST7735 and ST7789 Library'", "github": "https://github.com/adafruit/Adafruit-ST7735-Library", "dependencies": ["Adafruit_GFX", "Adafruit_BusIO"]}, "Adafruit_MPU6050": {"name": "Adafruit MPU6050", "version": ">=2.2.4", "author": "Adafruit", "description": "Library for MPU6050 accelerometer/gyroscope sensor", "required": true, "install_command": "Library Manager: 'Adafruit MPU6050'", "github": "https://github.com/adafruit/Adafruit_MPU6050", "dependencies": ["Adafruit_Unified_Sensor", "Adafruit_BusIO"]}, "Adafruit_HMC5883L": {"name": "Adafruit HMC5883L", "version": ">=1.2.1", "author": "Adafruit", "description": "Library for HMC5883L magnetometer sensor", "required": true, "install_command": "Library Manager: 'Adafruit HMC5883L'", "github": "https://github.com/adafruit/Adafruit_HMC5883_Unified", "dependencies": ["Adafruit_Unified_Sensor"]}, "Adafruit_MS5611": {"name": "Adafruit MS5611", "version": ">=1.0.0", "author": "Adafruit", "description": "Library for MS5611 barometric pressure sensor", "required": true, "install_command": "Library Manager: 'Adafruit MS5611'", "github": "https://github.com/adafruit/Adafruit_MS5611", "dependencies": ["Adafruit_Unified_Sensor", "Adafruit_BusIO"]}, "Adafruit_Unified_Sensor": {"name": "Adafruit Unified Sensor", "version": ">=1.1.7", "author": "Adafruit", "description": "Unified sensor driver interface for Adafruit sensors", "required": true, "install_command": "Library Manager: 'Adafruit Unified Sensor'", "github": "https://github.com/adafruit/Adafruit_Sensor", "dependencies": []}, "TinyGPSPlus": {"name": "TinyGPS++", "version": ">=1.0.3", "author": "<PERSON><PERSON>", "description": "Compact Arduino NMEA parsing library for GPS modules", "required": true, "install_command": "Library Manager: 'TinyGPS++'", "github": "https://github.com/mikalhart/TinyGPSPlus", "dependencies": []}, "Adafruit_BusIO": {"name": "Adafruit BusIO", "version": ">=1.14.1", "author": "Adafruit", "description": "Helper library for I2C and SPI communication", "required": true, "install_command": "Library Manager: 'Adafruit BusIO'", "github": "https://github.com/adafruit/Adafruit_BusIO", "dependencies": [], "note": "Usually installed automatically as dependency"}}}, "installation_instructions": {"arduino_ide": {"steps": ["1. Open Arduino IDE", "2. Go to Sketch > Include Library > Manage Libraries", "3. Search for each library name listed above", "4. <PERSON><PERSON> Install for each library", "5. Install all dependencies when prompted", "6. <PERSON><PERSON> IDE after installation"], "board_setup": ["1. Go to File > Preferences", "2. Add ESP32 board manager URL: https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json", "3. Go to Tools > Board > Boards Manager", "4. Search for 'ESP32' and install 'esp32 by Espressif Systems'", "5. Select 'ESP32 Dev Module' from Tools > Board menu"]}, "platformio": {"steps": ["1. Install PlatformIO Core or PlatformIO IDE", "2. Open project folder containing platformio.ini", "3. Run 'pio lib install' to install dependencies", "4. Build project with 'pio run'", "5. Upload with 'pio run --target upload'"]}}, "version_compatibility": {"arduino_ide": ">=1.8.19", "esp32_core": ">=2.0.0", "platformio": ">=6.0.0"}, "hardware_requirements": {"microcontroller": "ESP32 (any variant with sufficient GPIO)", "minimum_flash": "4MB", "minimum_ram": "520KB", "gpio_pins_required": 15}, "optional_libraries": {"WiFi": {"description": "For wireless telemetry and configuration", "use_case": "Remote monitoring and control"}, "BluetoothSerial": {"description": "For Bluetooth telemetry", "use_case": "Short-range wireless communication"}, "ArduinoOTA": {"description": "Over-the-air firmware updates", "use_case": "Wireless firmware updates"}, "EEPROM": {"description": "Non-volatile storage for calibration data", "use_case": "Persistent configuration storage"}}, "troubleshooting": {"common_issues": [{"issue": "Library not found during compilation", "solution": "Ensure all libraries are installed via Library Manager"}, {"issue": "Version conflicts between libraries", "solution": "Update all libraries to latest versions"}, {"issue": "ESP32 board not recognized", "solution": "Install ESP32 board package via Boards Manager"}, {"issue": "Compilation errors with Adafruit libraries", "solution": "Install Adafruit Unified Sensor and BusIO libraries"}]}, "build_information": {"compiler_flags": ["-DCORE_DEBUG_LEVEL=3", "-DARDUINO_ARCH_ESP32"], "optimization": "-O2", "warnings": "all"}}