/*
 * Motor Control implementation
 */

#include "motor_control.h"
#include "pid_controller.h"

// Motor data
MotorData motorOutputs;
bool motorsArmed = false;

bool initializeMotorControl() {
  // Configure PWM for motor control using newer ESP32 API
  ledcAttach(MOTOR_FR_PIN, PWM_FREQUENCY, PWM_RESOLUTION);
  ledcAttach(MOTOR_FL_PIN, PWM_FREQUENCY, PWM_RESOLUTION);
  ledcAttach(MOTOR_RR_PIN, PWM_FREQUENCY, PWM_RESOLUTION);
  ledcAttach(MOTOR_RL_PIN, PWM_FREQUENCY, PWM_RESOLUTION);
  
  // Initialize all motors to minimum (disarmed) state
  setAllMotors(MOTOR_ARM_PULSE);
  
  // Initialize motor output structure
  motorOutputs.frontRight = MOTOR_ARM_PULSE;
  motorOutputs.frontLeft = MOTOR_ARM_PULSE;
  motorOutputs.rearRight = MOTOR_ARM_PULSE;
  motorOutputs.rearLeft = MOTOR_ARM_PULSE;
  
  motorsArmed = false;
  
  Serial.println("Motor control initialized - Motors DISARMED");
  return true;
}

void updateMotorOutputs() {
  if (motorsArmed) {
    mixMotors();
    
    // Apply motor outputs
    setMotorSpeed(MOTOR_FR_PIN, motorOutputs.frontRight);
    setMotorSpeed(MOTOR_FL_PIN, motorOutputs.frontLeft);
    setMotorSpeed(MOTOR_RR_PIN, motorOutputs.rearRight);
    setMotorSpeed(MOTOR_RL_PIN, motorOutputs.rearLeft);
  } else {
    // Motors disarmed - set to minimum
    setAllMotors(MOTOR_ARM_PULSE);
  }
}

void mixMotors() {
  extern SystemState systemState;
  extern RCData rcData;
  
  // Get base throttle from RC input
  int baseThrottle = rcData.throttle;
  
  // Add altitude hold correction if active
  extern FlightMode currentFlightMode;
  if (currentFlightMode == ALTITUDE_HOLD_MODE || currentFlightMode == POSITION_HOLD_MODE) {
    baseThrottle += (int)altitudePID.output;
  }
  
  // Constrain base throttle
  baseThrottle = constrainMotor(baseThrottle);
  
  // Apply motor mixing for X-configuration
  motorOutputs.frontRight = baseThrottle + 
                           (MOTOR_FR_ROLL_COEFF * rollPID.output) +
                           (MOTOR_FR_PITCH_COEFF * pitchPID.output) +
                           (MOTOR_FR_YAW_COEFF * yawPID.output);
                           
  motorOutputs.frontLeft = baseThrottle + 
                          (MOTOR_FL_ROLL_COEFF * rollPID.output) +
                          (MOTOR_FL_PITCH_COEFF * pitchPID.output) +
                          (MOTOR_FL_YAW_COEFF * yawPID.output);
                          
  motorOutputs.rearRight = baseThrottle + 
                          (MOTOR_RR_ROLL_COEFF * rollPID.output) +
                          (MOTOR_RR_PITCH_COEFF * pitchPID.output) +
                          (MOTOR_RR_YAW_COEFF * yawPID.output);
                          
  motorOutputs.rearLeft = baseThrottle + 
                         (MOTOR_RL_ROLL_COEFF * rollPID.output) +
                         (MOTOR_RL_PITCH_COEFF * pitchPID.output) +
                         (MOTOR_RL_YAW_COEFF * yawPID.output);
  
  // Constrain all motor outputs to safe ranges
  motorOutputs.frontRight = constrainMotor(motorOutputs.frontRight);
  motorOutputs.frontLeft = constrainMotor(motorOutputs.frontLeft);
  motorOutputs.rearRight = constrainMotor(motorOutputs.rearRight);
  motorOutputs.rearLeft = constrainMotor(motorOutputs.rearLeft);
}

void armMotors() {
  if (checkArmingConditions()) {
    motorsArmed = true;
    Serial.println("MOTORS ARMED!");
    
    // Set motors to idle speed
    setAllMotors(MOTOR_IDLE_PULSE);
    
    // Flash LED to indicate armed state
    for (int i = 0; i < 3; i++) {
      digitalWrite(STATUS_LED_PIN, HIGH);
      delay(100);
      digitalWrite(STATUS_LED_PIN, LOW);
      delay(100);
    }
  } else {
    Serial.println("ARMING FAILED - Conditions not met!");
  }
}

void disarmMotors() {
  motorsArmed = false;
  setAllMotors(MOTOR_ARM_PULSE);
  Serial.println("MOTORS DISARMED");
  
  // Turn off status LED
  digitalWrite(STATUS_LED_PIN, LOW);
}

bool checkArmingConditions() {
  extern SystemState systemState;
  extern RCData rcData;
  
  // Check if throttle is at minimum
  if (!isThrottleLow()) {
    Serial.println("Arming failed: Throttle not at minimum");
    return false;
  }
  
  // Check if quadcopter is level
  if (!isQuadcopterLevel()) {
    Serial.println("Arming failed: Quadcopter not level");
    return false;
  }
  
  // Check if RC signal is valid
  if (rcData.signalLost) {
    Serial.println("Arming failed: RC signal lost");
    return false;
  }
  
  // Check if sensors are calibrated
  if (!systemState.calibrated) {
    Serial.println("Arming failed: Sensors not calibrated");
    return false;
  }
  
  // Check if failsafe is active
  if (systemState.failsafeActive) {
    Serial.println("Arming failed: Failsafe active");
    return false;
  }
  
  // Check battery voltage
  if (systemState.batteryVoltage < BATTERY_MIN_VOLTAGE) {
    Serial.println("Arming failed: Battery voltage too low");
    return false;
  }
  
  return true;
}

void setMotorSpeed(int pin, int speed) {
  // Convert microsecond pulse width to PWM duty cycle
  int pwmValue = mapPWMToESC(speed);
  ledcWrite(pin, pwmValue);
}

void setAllMotors(int speed) {
  setMotorSpeed(MOTOR_FR_PIN, speed);
  setMotorSpeed(MOTOR_FL_PIN, speed);
  setMotorSpeed(MOTOR_RR_PIN, speed);
  setMotorSpeed(MOTOR_RL_PIN, speed);
}

void testMotors() {
  Serial.println("Testing motors - ENSURE PROPELLERS ARE REMOVED!");
  delay(3000);
  
  // Test each motor individually
  Serial.println("Testing Front Right motor...");
  setMotorSpeed(MOTOR_FR_PIN, MOTOR_IDLE_PULSE);
  delay(2000);
  setMotorSpeed(MOTOR_FR_PIN, MOTOR_ARM_PULSE);
  delay(1000);

  Serial.println("Testing Front Left motor...");
  setMotorSpeed(MOTOR_FL_PIN, MOTOR_IDLE_PULSE);
  delay(2000);
  setMotorSpeed(MOTOR_FL_PIN, MOTOR_ARM_PULSE);
  delay(1000);

  Serial.println("Testing Rear Right motor...");
  setMotorSpeed(MOTOR_RR_PIN, MOTOR_IDLE_PULSE);
  delay(2000);
  setMotorSpeed(MOTOR_RR_PIN, MOTOR_ARM_PULSE);
  delay(1000);

  Serial.println("Testing Rear Left motor...");
  setMotorSpeed(MOTOR_RL_PIN, MOTOR_IDLE_PULSE);
  delay(2000);
  setMotorSpeed(MOTOR_RL_PIN, MOTOR_ARM_PULSE);
  delay(1000);
  
  Serial.println("Motor test complete!");
}

bool isThrottleLow() {
  extern RCData rcData;
  return (rcData.throttle <= (RC_MIN_PULSE + 50));
}

bool isQuadcopterLevel() {
  extern SystemState systemState;
  return (abs(systemState.roll) < 10.0 && abs(systemState.pitch) < 10.0);
}

void emergencyStop() {
  disarmMotors();
  Serial.println("EMERGENCY STOP ACTIVATED!");
}

int mapPWMToESC(int pulseWidth) {
  // Map microsecond pulse width (1000-2000) to PWM duty cycle (0-4095)
  return map(pulseWidth, MOTOR_MIN_PULSE, MOTOR_MAX_PULSE, 0, PWM_MAX_VALUE);
}

int constrainMotor(int value) {
  if (value < MOTOR_MIN_PULSE) return MOTOR_MIN_PULSE;
  if (value > MOTOR_MAX_PULSE) return MOTOR_MAX_PULSE;
  return value;
}
