/*
 * Configuration file for ESP32 Quadcopter Flight Controller
 * 
 * This file contains all pin definitions, constants, and configuration
 * parameters for the quadcopter system.
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <HardwareSerial.h>

// ============================================================================
// PIN DEFINITIONS
// ============================================================================

// I2C pins for GY-86 sensor module
#define I2C_SDA_PIN         21
#define I2C_SCL_PIN         22

// GPS UART pins (NEO-6M)
#define GPS_RX_PIN          16
#define GPS_TX_PIN          17

// TFT Display SPI pins
#define TFT_CS_PIN          5
#define TFT_DC_PIN          2
#define TFT_RST_PIN         4
#define TFT_MOSI_PIN        23
#define TFT_SCLK_PIN        18

// RC Receiver PWM input pins
#define RC_THROTTLE_PIN     14
#define RC_ROLL_PIN         13
#define RC_PITCH_PIN        12
#define RC_YAW_PIN          27
#define RC_MODE_PIN         26
#define RC_AUX1_PIN         25
#define RC_AUX2_PIN         33

// ESC PWM output pins (motor control)
#define MOTOR_FR_PIN        32  // Front Right
#define MOTOR_FL_PIN        15  // Front Left
#define MOTOR_RR_PIN        19  // Rear Right
#define MOTOR_RL_PIN        0   // Rear Left

// Status LED and buzzer
#define STATUS_LED_PIN      2  // Built-in LED on most ESP32 boards
#define BUZZER_PIN          35

#define DISPLAY_BUTTON_PIN  36  // Button to toggle display pages

// Battery voltage monitoring
#define BATTERY_PIN         34
#define VOLTAGE_DIVIDER_RATIO 11.0  // Adjust based on your voltage divider

// ============================================================================
// SYSTEM CONSTANTS
// ============================================================================

// Loop timing (milliseconds)
#define MAIN_LOOP_RATE      250     // 250Hz main loop
#define GPS_UPDATE_INTERVAL 200     // 5Hz GPS updates
#define DISPLAY_UPDATE_INTERVAL 100 // 10Hz display updates
#define TELEMETRY_UPDATE_INTERVAL 50 // 20Hz telemetry

// RC signal parameters
#define RC_MIN_PULSE        1000    // Minimum PWM pulse width (microseconds)
#define RC_MAX_PULSE        2000    // Maximum PWM pulse width (microseconds)
#define RC_MID_PULSE        1500    // Center PWM pulse width (microseconds)
#define RC_DEADBAND         10      // RC input deadband (microseconds)
#define RC_TIMEOUT          100     // RC signal timeout (milliseconds)

// Motor parameters
#define MOTOR_MIN_PULSE     1000    // Minimum ESC pulse width
#define MOTOR_MAX_PULSE     2000    // Maximum ESC pulse width
#define MOTOR_ARM_PULSE     1000    // ESC arming pulse width
#define MOTOR_IDLE_PULSE    1100    // Motor idle speed

// Flight parameters
#define MAX_ANGLE           30.0    // Maximum roll/pitch angle (degrees)
#define MAX_YAW_RATE        200.0   // Maximum yaw rate (degrees/second)
#define MAX_CLIMB_RATE      5.0     // Maximum climb rate (m/s)

// Battery monitoring
#define BATTERY_CELLS       3       // Number of battery cells (3S LiPo)
#define BATTERY_MIN_VOLTAGE 10.5    // Minimum safe voltage (3.5V per cell)
#define BATTERY_MAX_VOLTAGE 12.6    // Maximum voltage (4.2V per cell)

// GPS parameters
#define GPS_BAUD_RATE       38400
#define GPS_MIN_SATELLITES  6
#define GPS_MAX_HDOP        2.0

// Sensor parameters
#define GYRO_SCALE          131.0   // LSB/°/s for ±250°/s range
#define ACCEL_SCALE         16384.0 // LSB/g for ±2g range
#define MAG_SCALE           1090.0  // LSB/Gauss

// ============================================================================
// FLIGHT MODES
// ============================================================================

enum FlightMode {
  ACRO_MODE = 0,              // Rate/Acro mode - direct gyro control
  STABILIZE_MODE = 1,         // Angle mode - self-leveling
  ALTITUDE_HOLD_MODE = 2,     // Altitude hold using barometer
  POSITION_HOLD_MODE = 3,     // GPS position hold
  RETURN_TO_LAUNCH_MODE = 4,  // Autonomous return to launch
  LAND_MODE = 5               // Autonomous landing
};

// ============================================================================
// DATA STRUCTURES
// ============================================================================

struct SensorData {
  // IMU data
  float accelX, accelY, accelZ;     // Accelerometer (m/s²)
  float gyroX, gyroY, gyroZ;        // Gyroscope (°/s)
  float magX, magY, magZ;           // Magnetometer (µT)
  
  // Barometer data
  float pressure;                    // Pressure (hPa)
  float temperature;                 // Temperature (°C)
  float altitude;                    // Altitude (m)
  
  // Calibration offsets
  float accelOffsetX, accelOffsetY, accelOffsetZ;
  float gyroOffsetX, gyroOffsetY, gyroOffsetZ;
  float magOffsetX, magOffsetY, magOffsetZ;
  float groundPressure;
};

struct GPSData {
  double latitude;                   // Latitude (degrees)
  double longitude;                  // Longitude (degrees)
  float altitude;                    // GPS altitude (m)
  float speed;                       // Ground speed (m/s)
  float course;                      // Course over ground (degrees)
  int satellites;                    // Number of satellites
  float hdop;                        // Horizontal dilution of precision
  bool fix;                          // GPS fix status
  unsigned long lastUpdate;          // Last GPS update time
};

struct RCData {
  int throttle;                      // Throttle input (1000-2000)
  int roll;                          // Roll input (1000-2000)
  int pitch;                         // Pitch input (1000-2000)
  int yaw;                           // Yaw input (1000-2000)
  int mode;                          // Flight mode switch (1000-2000)
  int aux1;                          // Auxiliary channel 1
  int aux2;                          // Auxiliary channel 2
  bool signalLost;                   // RC signal lost flag
  unsigned long lastUpdate;          // Last RC update time
};

struct PIDData {
  float kp, ki, kd;                  // PID gains
  float error;                       // Current error
  float previousError;               // Previous error
  float integral;                    // Integral term
  float derivative;                  // Derivative term
  float output;                      // PID output
  float integralLimit;               // Integral windup limit
  float outputLimit;                 // Output limit
};

struct MotorData {
  int frontRight;                    // Front right motor PWM
  int frontLeft;                     // Front left motor PWM
  int rearRight;                     // Rear right motor PWM
  int rearLeft;                      // Rear left motor PWM
};

struct SystemState {
  // Attitude (filtered sensor fusion output)
  float roll;                        // Roll angle (degrees)
  float pitch;                       // Pitch angle (degrees)
  float yaw;                         // Yaw angle (degrees)
  float altitude;                    // Current altitude (m)
  
  // Rates
  float rollRate;                    // Roll rate (°/s)
  float pitchRate;                   // Pitch rate (°/s)
  float yawRate;                     // Yaw rate (°/s)
  float climbRate;                   // Climb rate (m/s)
  
  // System status
  bool armed;                        // Motor armed status
  bool calibrated;                   // Sensor calibration status
  bool failsafeActive;               // Failsafe active flag
  float batteryVoltage;              // Battery voltage (V)
  float cpuLoad;                     // CPU load percentage
  
  // Data structures
  SensorData sensors;
  GPSData gpsData;
  RCData rcData;
  MotorData motors;
};

// ============================================================================
// PID TUNING PARAMETERS
// ============================================================================

// Roll PID gains
#define ROLL_KP             1.2
#define ROLL_KI             0.02
#define ROLL_KD             0.5

// Pitch PID gains
#define PITCH_KP            1.2
#define PITCH_KI            0.02
#define PITCH_KD            0.5

// Yaw PID gains
#define YAW_KP              2.0
#define YAW_KI              0.1
#define YAW_KD              0.2

// Altitude PID gains
#define ALT_KP              2.0
#define ALT_KI              0.5
#define ALT_KD              1.0

// Position PID gains
#define POS_KP              1.0
#define POS_KI              0.1
#define POS_KD              0.5

// PID limits
#define PID_INTEGRAL_LIMIT  100.0
#define PID_OUTPUT_LIMIT    400.0

#endif // CONFIG_H
