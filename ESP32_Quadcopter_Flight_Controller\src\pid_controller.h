/*
 * PID Controller for ESP32 Quadcopter Flight Controller
 * 
 * This module implements PID control for:
 * - Roll stabilization
 * - Pitch stabilization  
 * - Yaw stabilization
 * - Altitude hold
 * - Position hold
 */

#ifndef PID_CONTROLLER_H
#define PID_CONTROLLER_H

#include "config.h"

// PID controller structure
struct PIDController {
  float kp, ki, kd;           // PID gains
  float error;                // Current error
  float previousError;        // Previous error
  float integral;             // Integral accumulator
  float derivative;           // Derivative term
  float output;               // PID output
  float integralLimit;        // Integral windup limit
  float outputLimit;          // Output saturation limit
  unsigned long lastTime;     // Last calculation time
};

// PID controllers for each axis
extern PIDController rollPID;
extern PIDController pitchPID;
extern PIDController yawPID;
extern PIDController altitudePID;
extern PIDController positionXPID;
extern PIDController positionYPID;

// Setpoints (desired values)
extern float rollSetpoint;
extern float pitchSetpoint;
extern float yawSetpoint;
extern float altitudeSetpoint;
extern float positionXSetpoint;
extern float positionYSetpoint;

// Function declarations
void initializePIDControllers();
void calculatePIDOutputs(float deltaTime);
float calculatePID(PIDController* pid, float setpoint, float input, float deltaTime);
void resetPIDIntegrators();
void updatePIDGains();
void setPIDGains(PIDController* pid, float kp, float ki, float kd);
void setPIDLimits(PIDController* pid, float integralLimit, float outputLimit);

// Flight mode specific PID calculations
void calculateStabilizePID(float deltaTime);
void calculateAcroPID(float deltaTime);
void calculateAltitudeHoldPID(float deltaTime);
void calculatePositionHoldPID(float deltaTime);

// Utility functions
float constrainFloat(float value, float min, float max);
void antiWindup(PIDController* pid);
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);

#endif // PID_CONTROLLER_H
