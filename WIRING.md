# Wiring Guide - ESP32 Quadcopter Flight Controller

This guide provides detailed wiring instructions for connecting all components to the ESP32 microcontroller.

## Overview

The ESP32 serves as the central flight controller, interfacing with:
- GY-86 10DOF sensor module (I2C)
- NEO-6M GPS module (UART)
- 1.8" TFT display (SPI)
- RC receiver (PWM inputs)
- ESCs/Motors (PWM outputs)
- Additional peripherals

## Power Distribution

### Main Power Requirements
- **ESP32**: 3.3V (regulated from 5V)
- **GY-86 Module**: 3.3V or 5V (check your module)
- **GPS Module**: 3.3V or 5V (check your module)
- **TFT Display**: 3.3V
- **RC Receiver**: 5V (from BEC or separate regulator)
- **ESCs**: Direct from main battery (7.4V-12.6V for 3S LiPo)

### Power Distribution Board (Recommended)
Use a PDB with integrated 5V and 3.3V regulators:
- Main battery → PDB
- PDB 5V → RC receiver, servo rail
- PDB 3.3V → ESP32, sensors, display

## Detailed Wiring

### ESP32 Pinout Reference
```
                    ESP32 DevKit V1
                   ┌─────────────────┐
               EN  │1              30│ GPIO23 (MOSI)
          GPIO36   │2              29│ GPIO22 (SCL)
          GPIO39   │3              28│ GPIO1 (TX)
          GPIO34   │4              27│ GPIO3 (RX)
          GPIO35   │5              26│ GPIO21 (SDA)
          GPIO32   │6              25│ GND
          GPIO33   │7              24│ GPIO19
          GPIO25   │8              23│ GPIO18 (SCLK)
          GPIO26   │9              22│ GPIO5
          GPIO27   │10             21│ GPIO17
          GPIO14   │11             20│ GPIO16
          GPIO12   │12             19│ GPIO4
          GND      │13             18│ GPIO0
          GPIO13   │14             17│ GPIO2
          GPIO15   │15             16│ 3V3
                   └─────────────────┘
```

### 1. GY-86 10DOF Module (I2C)

**Connections:**
```
GY-86 Module    →    ESP32
VCC             →    3.3V
GND             →    GND
SDA             →    GPIO21
SCL             →    GPIO22
```

**Notes:**
- Use short, twisted wires for I2C connections
- Add 4.7kΩ pull-up resistors if experiencing communication issues
- Mount module with minimal vibration
- Ensure proper orientation (arrow pointing forward)

### 2. NEO-6M GPS Module (UART)

**Connections:**
```
GPS Module      →    ESP32
VCC             →    3.3V or 5V (check module)
GND             →    GND
RX              →    GPIO17 (TX2)
TX              →    GPIO16 (RX2)
```

**Notes:**
- Mount GPS antenna away from other electronics
- Use ceramic antenna or external active antenna
- Keep antenna horizontal for best reception
- Avoid metal obstructions above antenna

### 3. 1.8" TFT Display (SPI)

**Connections:**
```
TFT Display     →    ESP32
VCC             →    3.3V
GND             →    GND
CS              →    GPIO5
DC (A0)         →    GPIO2
RST             →    GPIO4
MOSI (SDA)      →    GPIO23
SCLK (SCL)      →    GPIO18
```

**Notes:**
- Use short wires for SPI connections
- Display should be easily visible during flight
- Consider adding protective cover
- Verify display driver (ST7735 vs ST7789)

### 4. RC Receiver (PWM Inputs)

**Connections:**
```
RC Channel      →    ESP32 Pin    →    Function
Channel 1       →    GPIO13       →    Roll
Channel 2       →    GPIO12       →    Pitch  
Channel 3       →    GPIO14       →    Throttle
Channel 4       →    GPIO27       →    Yaw
Channel 5       →    GPIO26       →    Flight Mode
Channel 6       →    GPIO25       →    Aux 1
Channel 7       →    GPIO33       →    Aux 2
```

**RC Receiver Power:**
```
RC Receiver     →    Power Source
VCC (+)         →    5V (from BEC or regulator)
GND (-)         →    GND
```

**Notes:**
- Use individual signal wires for each channel
- Ensure receiver is bound to transmitter
- Test all channels before flight
- Consider receiver placement for best signal reception

### 5. ESCs and Motors (PWM Outputs)

**Connections:**
```
Motor Position  →    ESP32 Pin    →    ESC Signal Wire
Front Right     →    GPIO32       →    ESC1 Signal
Front Left      →    GPIO15       →    ESC2 Signal
Rear Right      →    GPIO19       →    ESC3 Signal
Rear Left       →    GPIO0        →    ESC4 Signal
```

**ESC Power Distribution:**
```
ESC Power       →    Connection
ESC Red (+)     →    Main Battery Positive
ESC Black (-)   →    Main Battery Negative (via PDB)
ESC Signal      →    ESP32 GPIO (as above)
ESC Ground      →    ESP32 GND (common ground)
```

**Motor Configuration (X-Frame):**
```
    Front
  CW    CCW
    \  /
     \/
     /\
    /  \
  CCW   CW
    Rear

Front Left (FL)  - Counter-Clockwise
Front Right (FR) - Clockwise  
Rear Left (RL)   - Clockwise
Rear Right (RR)  - Counter-Clockwise
```

### 6. Additional Components

**Status LED:**
```
Component       →    ESP32
Built-in LED    →    GPIO2 (usually built-in)
```

**Buzzer (Optional):**
```
Buzzer          →    ESP32
Positive        →    GPIO35
Negative        →    GND
```

**Battery Monitor:**
```
Component       →    ESP32
Voltage Divider →    GPIO34 (ADC)
```

**Voltage Divider Circuit:**
```
Battery+ ──[10kΩ]──┬──[1kΩ]── GND
                   │
                   └── GPIO34 (Max 3.3V)
```

## Wiring Best Practices

### 1. Wire Management
- Use different colored wires for different functions
- Keep power and signal wires separated
- Use twisted pairs for differential signals
- Secure all connections with heat shrink or connectors

### 2. Grounding
- Establish single-point ground system
- Connect all component grounds to ESP32 GND
- Use thick wire for ground connections
- Avoid ground loops

### 3. Power Distribution
- Use adequate wire gauge for current requirements
- Add filtering capacitors near power inputs
- Consider using a power distribution board
- Monitor voltage levels under load

### 4. Signal Integrity
- Keep signal wires as short as possible
- Avoid running signal wires parallel to power wires
- Use shielded cable for sensitive signals
- Add pull-up resistors where needed

### 5. Mechanical Considerations
- Secure all connections against vibration
- Use strain relief on all cables
- Mount components to minimize mechanical stress
- Consider connector types for easy maintenance

## Testing Connections

### 1. Power Test
1. Connect only power wires
2. Measure voltages at each component
3. Verify no short circuits
4. Check current consumption

### 2. Communication Test
1. Upload test firmware
2. Verify I2C device detection
3. Test UART communication
4. Check SPI display functionality

### 3. Signal Test
1. Test RC receiver signal quality
2. Verify PWM output to ESCs
3. Check all GPIO functions
4. Test interrupt-based inputs

### 4. Integration Test
1. Connect all components
2. Upload full firmware
3. Verify all systems operational
4. Check for interference issues

## Common Wiring Issues

### Problem: I2C devices not detected
- **Solution**: Check SDA/SCL connections, add pull-up resistors

### Problem: GPS not getting fix
- **Solution**: Check antenna placement, verify UART connections

### Problem: Display not working
- **Solution**: Verify SPI connections, check power supply

### Problem: RC signal intermittent
- **Solution**: Check receiver binding, verify power supply

### Problem: Motors not responding
- **Solution**: Check ESC calibration, verify PWM connections

## Safety Checklist

Before powering on:
- [ ] All connections secure
- [ ] No short circuits
- [ ] Proper wire gauge used
- [ ] Components properly mounted
- [ ] Battery connections correct
- [ ] Emergency stop accessible

## Troubleshooting Tools

- Multimeter for voltage/continuity testing
- Oscilloscope for signal analysis
- Logic analyzer for digital communication
- Current meter for power consumption
- Servo tester for PWM verification
