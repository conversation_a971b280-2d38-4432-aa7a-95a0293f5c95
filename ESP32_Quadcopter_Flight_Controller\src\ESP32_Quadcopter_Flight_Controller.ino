/*
 * ESP32 Quadcopter Flight Controller
 * 
 * A comprehensive flight control system for ESP32-based quadcopters
 * Features:
 * - 10DOF IMU (MPU6050 + HMC5883L + MS5611)
 * - GPS positioning (NEO-6M)
 * - RC control (FS-TH9X)
 * - TFT display
 * - PID stabilization
 * - Multiple flight modes
 * - Failsafe mechanisms
 * 
 * Author: ESP32 Quadcopter Project
 * Version: 1.0
 * Date: 2025
 */

#include "config.h"
#include "sensors.h"
#include "pid_controller.h"
#include "rc_input.h"
#include "motor_control.h"
#include "gps_handler.h"
#include "display.h"
#include "failsafe.h"

// Global system state
SystemState systemState;
FlightMode currentFlightMode = STABILIZE_MODE;
bool motorsArmed = false;
bool systemInitialized = false;

// External declarations for global variables
extern RCData rcData;

// Function declarations
const char* getFlightModeName(FlightMode mode);

// Timing variables
unsigned long lastLoopTime = 0;
unsigned long lastDisplayUpdate = 0;
unsigned long lastGPSUpdate = 0;
unsigned long lastTelemetryUpdate = 0;

void setup() {
  Serial.begin(115200);
  Serial.println("ESP32 Quadcopter Flight Controller Starting...");
  
  // Initialize all subsystems
  if (!initializeSystem()) {
    Serial.println("CRITICAL ERROR: System initialization failed!");
    while(1) {
      // Flash LED to indicate error
      digitalWrite(2, HIGH);
      delay(100);
      digitalWrite(2, LOW);
      delay(100);
    }
  }
  
  Serial.println("System initialization complete!");
  Serial.println("Ready for flight operations.");
  systemInitialized = true;
}

void loop() {
  unsigned long currentTime = micros();
  float deltaTime = (currentTime - lastLoopTime) / 1000000.0f;
  lastLoopTime = currentTime;
  
  // Main flight control loop - runs at high frequency
  if (systemInitialized) {
    // Read all sensors
    updateSensors();
    
    // Process RC inputs
    updateRCInputs();
    
    // Update GPS data (lower frequency)
    if (millis() - lastGPSUpdate > GPS_UPDATE_INTERVAL) {
      updateGPS();
      lastGPSUpdate = millis();
    }
    
    // Check failsafe conditions
    checkFailsafe();
    
    // Determine flight mode
    updateFlightMode();
    
    // Calculate PID outputs based on current flight mode
    if (motorsArmed && !systemState.failsafeActive) {
      calculatePIDOutputs(deltaTime);
      
      // Update motor outputs
      updateMotorOutputs();
    } else {
      // Motors disarmed or failsafe active - stop all motors
      disarmMotors();
    }
    
    // Update display (lower frequency)
    if (millis() - lastDisplayUpdate > DISPLAY_UPDATE_INTERVAL) {
      updateDisplay();
      lastDisplayUpdate = millis();
    }
    
    // Send telemetry data (lower frequency)
    if (millis() - lastTelemetryUpdate > TELEMETRY_UPDATE_INTERVAL) {
      sendTelemetry();
      lastTelemetryUpdate = millis();
    }
  }
  
  // Small delay to prevent watchdog issues
  delayMicroseconds(100);
}

bool initializeSystem() {
  Serial.println("Initializing system components...");
  
  // Initialize GPIO pins
  pinMode(2, OUTPUT);
  digitalWrite(2, LOW);
  
  // Initialize I2C bus for sensors
  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
  Wire.setClock(100000); // 100kHz I2C speed (slower, more reliable)

  // Test I2C bus
  Serial.println("Scanning I2C bus...");
  int deviceCount = 0;
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    byte error = Wire.endTransmission();
    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
      deviceCount++;
    }
  }
  Serial.print("Found ");
  Serial.print(deviceCount);
  Serial.println(" I2C devices");
  
  // Initialize sensors
  Serial.println("Initializing sensors...");
  if (!initializeSensors()) {
    Serial.println("ERROR: Sensor initialization failed!");
    return false;
  }
  
  // Initialize RC input
  Serial.println("Initializing RC input...");
  if (!initializeRCInput()) {
    Serial.println("ERROR: RC input initialization failed!");
    return false;
  }
  
  // Initialize motor control
  Serial.println("Initializing motor control...");
  if (!initializeMotorControl()) {
    Serial.println("ERROR: Motor control initialization failed!");
    return false;
  }
  
  // Initialize GPS
  Serial.println("Initializing GPS...");
  if (!initializeGPS()) {
    Serial.println("WARNING: GPS initialization failed - continuing without GPS");
  }
  
  // Initialize display
  Serial.println("Initializing display...");
  if (!initializeDisplay()) {
    Serial.println("WARNING: Display initialization failed - continuing without display");
  }
  
  // Initialize PID controllers
  Serial.println("Initializing PID controllers...");
  initializePIDControllers();
  
  // Initialize failsafe system
  Serial.println("Initializing failsafe system...");
  initializeFailsafe();
  
  // Perform initial sensor calibration check
  Serial.println("Checking sensor calibration...");
  if (!checkSensorCalibration()) {
    Serial.println("WARNING: Sensors may need calibration!");
  }
  
  Serial.println("System initialization completed successfully!");
  return true;
}

void updateFlightMode() {
  FlightMode newMode = determineFlightModeFromRC();
  
  if (newMode != currentFlightMode) {
    Serial.print("Flight mode changed from ");
    Serial.print(getFlightModeName(currentFlightMode));
    Serial.print(" to ");
    Serial.println(getFlightModeName(newMode));
    
    currentFlightMode = newMode;
    
    // Reset PID integrators when changing modes
    resetPIDIntegrators();
    
    // Update display to show new mode
    displayFlightMode(currentFlightMode);
  }
}

void sendTelemetry() {
  // Send telemetry data over Serial for ground station or debugging
  Serial.print("TELEM,");
  Serial.print(millis());
  Serial.print(",");
  Serial.print(systemState.roll, 2);
  Serial.print(",");
  Serial.print(systemState.pitch, 2);
  Serial.print(",");
  Serial.print(systemState.yaw, 2);
  Serial.print(",");
  Serial.print(systemState.altitude, 2);
  Serial.print(",");
  Serial.print(systemState.gpsData.latitude, 6);
  Serial.print(",");
  Serial.print(systemState.gpsData.longitude, 6);
  Serial.print(",");
  Serial.print(systemState.gpsData.satellites);
  Serial.print(",");
  Serial.print(systemState.batteryVoltage, 2);
  Serial.print(",");
  Serial.print(getFlightModeName(currentFlightMode));
  Serial.print(",");
  Serial.print(motorsArmed ? "ARMED" : "DISARMED");
  Serial.print(",");
  Serial.println(systemState.failsafeActive ? "FAILSAFE" : "NORMAL");
}

const char* getFlightModeName(FlightMode mode) {
  switch(mode) {
    case ACRO_MODE: return "ACRO";
    case STABILIZE_MODE: return "STABILIZE";
    case ALTITUDE_HOLD_MODE: return "ALT_HOLD";
    case POSITION_HOLD_MODE: return "POS_HOLD";
    case RETURN_TO_LAUNCH_MODE: return "RTL";
    case LAND_MODE: return "LAND";
    default: return "UNKNOWN";
  }
}
