# Required Arduino Libraries for ESP32 Quadcopter Flight Controller
# Install these libraries through Arduino IDE Library Manager

# Core ESP32 Libraries (usually pre-installed)
Wire                    # I2C communication
SPI                     # SPI communication  
HardwareSerial         # UART communication

# Adafruit Sensor Libraries
Adafruit GFX Library           # Core graphics library for displays
Adafruit ST7735 and ST7789 Library  # TFT display driver
Adafruit MPU6050              # MPU6050 accelerometer/gyroscope
Adafruit HMC5883L             # HMC5883L magnetometer  
Adafruit MS5611               # MS5611 barometer
Adafruit Unified Sensor       # Unified sensor driver interface

# GPS Library
TinyGPS++                     # GPS NMEA parsing library

# Installation Instructions:
# 1. Open Arduino IDE
# 2. Go to Sketch > Include Library > Manage Libraries
# 3. Search for each library name above
# 4. Click Install for each library
# 5. Make sure to install all dependencies when prompted

# Alternative Installation via Library Manager URLs:
# Some libraries may require adding board manager URLs first:
# File > Preferences > Additional Boards Manager URLs:
# https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json

# ESP32 Board Package:
# Tools > Board > Boards Manager > Search "ESP32" > Install "esp32 by Espressif Systems"
