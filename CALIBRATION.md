# Sensor Calibration Guide

Proper sensor calibration is critical for stable flight. This guide provides step-by-step procedures for calibrating all sensors in the ESP32 quadcopter flight controller.

## Overview

The following sensors require calibration:
- **MPU6050 Accelerometer** - Compensates for mounting bias
- **MPU6050 Gyroscope** - Eliminates drift when stationary  
- **HMC5883L Magnetometer** - Corrects for magnetic interference
- **MS5611 Barometer** - Sets ground pressure reference
- **RC Transmitter** - Maps stick positions to control ranges

## Pre-Calibration Setup

### Requirements
- Quadcopter fully assembled
- All components connected and powered
- Arduino IDE with Serial Monitor
- Level surface for calibration
- Open area away from metal objects (for magnetometer)

### Initial Checks
1. Upload firmware to ESP32
2. Open Serial Monitor (115200 baud)
3. Verify all sensors are detected
4. Ensure stable power supply
5. Check for any error messages

## 1. Accelerometer Calibration

### Purpose
Compensates for sensor mounting bias and ensures accurate attitude calculation.

### Procedure
1. **Preparation**
   - Place quadcopter on perfectly level surface
   - Ensure frame is not moving or vibrating
   - Remove propellers for safety

2. **Start Calibration**
   - Send command: `cal_accel` via Serial Monitor
   - Or use dedicated calibration function in code
   - Keep quadcopter absolutely still during process

3. **Data Collection**
   - System will collect 1000+ samples
   - Process takes approximately 10-15 seconds
   - Do not move quadcopter during this time

4. **Verification**
   - Check Serial Monitor for completion message
   - Verify offset values are reasonable
   - Test by tilting quadcopter - attitude should respond correctly

### Expected Results
```
Accelerometer Calibration Complete!
X Offset: -0.15 m/s²
Y Offset: 0.08 m/s²  
Z Offset: -0.23 m/s²
```

### Troubleshooting
- **Large offsets (>2.0)**: Check sensor mounting, repeat calibration
- **Calibration fails**: Verify I2C connections, check power supply
- **Unstable readings**: Eliminate vibrations, use better mounting

## 2. Gyroscope Calibration

### Purpose
Eliminates gyroscope drift when quadcopter is stationary.

### Procedure
1. **Preparation**
   - Place quadcopter on stable, vibration-free surface
   - Ensure no air currents or movement
   - Wait for temperature stabilization (2-3 minutes)

2. **Start Calibration**
   - Send command: `cal_gyro` via Serial Monitor
   - Keep quadcopter absolutely motionless
   - Avoid any vibrations or air movement

3. **Data Collection**
   - System collects 2000+ samples
   - Process takes 20-30 seconds
   - Critical to maintain perfect stillness

4. **Verification**
   - Check for completion message
   - Verify gyro rates are near zero when stationary
   - Test by rotating quadcopter - rates should respond

### Expected Results
```
Gyroscope Calibration Complete!
X Rate Offset: 0.02 °/s
Y Rate Offset: -0.01 °/s
Z Rate Offset: 0.03 °/s
```

### Troubleshooting
- **High offsets (>5°/s)**: Eliminate all vibrations, repeat
- **Unstable readings**: Check mounting, temperature effects
- **Calibration timeout**: Verify sensor communication

## 3. Magnetometer Calibration

### Purpose
Compensates for hard-iron and soft-iron magnetic distortions from quadcopter components.

### Procedure
1. **Preparation**
   - Move to open area away from metal objects
   - Avoid cars, buildings, power lines
   - Remove any magnetic materials from vicinity

2. **Start Calibration**
   - Send command: `cal_mag` via Serial Monitor
   - Begin figure-8 rotation pattern immediately
   - Continue for full 30-60 seconds

3. **Rotation Pattern**
   - Slowly rotate quadcopter around all three axes
   - Perform figure-8 motions in multiple orientations
   - Ensure all possible orientations are covered
   - Maintain smooth, continuous motion

4. **Data Collection**
   - System tracks min/max values for each axis
   - Continue until prompted to stop
   - More rotations = better calibration

5. **Verification**
   - Check completion message
   - Verify heading changes smoothly when rotating
   - Test compass accuracy with known directions

### Expected Results
```
Magnetometer Calibration Complete!
X Offset: 125.3 µT
Y Offset: -87.6 µT
Z Offset: 203.1 µT
Hard Iron Compensation Applied
```

### Troubleshooting
- **Poor heading accuracy**: Repeat in different location
- **Erratic compass**: Check for magnetic interference
- **Calibration incomplete**: Ensure full rotation coverage

## 4. Barometer Calibration

### Purpose
Sets ground pressure reference for accurate altitude calculation.

### Procedure
1. **Preparation**
   - Ensure quadcopter is at desired "zero" altitude
   - Wait for atmospheric pressure stabilization
   - Avoid windy conditions

2. **Start Calibration**
   - Send command: `cal_baro` via Serial Monitor
   - System automatically takes ground pressure reading
   - Process completes quickly (5-10 seconds)

3. **Verification**
   - Check ground pressure value is reasonable (950-1050 hPa)
   - Verify altitude reads near zero at ground level
   - Test by lifting quadcopter - altitude should increase

### Expected Results
```
Barometer Calibration Complete!
Ground Pressure: 1013.25 hPa
Current Altitude: 0.0 m
```

### Troubleshooting
- **Unreasonable pressure**: Check sensor, environmental conditions
- **Altitude drift**: Re-calibrate, check temperature effects
- **No response**: Verify I2C communication

## 5. RC Transmitter Calibration

### Purpose
Maps RC stick positions to proper control ranges and centers.

### Procedure
1. **Preparation**
   - Turn on RC transmitter
   - Verify receiver is bound and receiving signal
   - Check all channels are responding

2. **Center Position Calibration**
   - Center all sticks and switches
   - Send command: `cal_rc_center`
   - System records center positions

3. **Range Calibration**
   - Send command: `cal_rc_range`
   - Move each stick to full extremes
   - Hold each position for 2-3 seconds
   - Cover all stick positions thoroughly

4. **Verification**
   - Check all channels read 1000-2000 µs
   - Verify center positions are ~1500 µs
   - Test full stick deflections

### Expected Results
```
RC Calibration Complete!
Throttle: 1000-2000 µs (Center: 1000)
Roll: 1000-2000 µs (Center: 1500)
Pitch: 1000-2000 µs (Center: 1500)
Yaw: 1000-2000 µs (Center: 1500)
Mode: 1000-2000 µs
```

### Troubleshooting
- **Limited range**: Check transmitter settings, EPA values
- **Off-center**: Adjust transmitter trims
- **No signal**: Check binding, receiver power

## Calibration Storage

### Saving Calibration Data
The system automatically saves calibration values to EEPROM/Flash memory:
- Values persist through power cycles
- No need to recalibrate every startup
- Can be manually reset if needed

### Backup Calibration Values
Record your calibration values for backup:
```
Accelerometer Offsets: X=___ Y=___ Z=___
Gyroscope Offsets: X=___ Y=___ Z=___
Magnetometer Offsets: X=___ Y=___ Z=___
Ground Pressure: ___ hPa
RC Centers: T=___ R=___ P=___ Y=___
```

## Calibration Verification

### Flight Test Preparation
1. **Sensor Check**
   - Verify attitude display matches physical orientation
   - Check compass heading accuracy
   - Confirm altitude readings are stable

2. **RC Response Check**
   - Test all stick movements
   - Verify flight mode switching
   - Check auxiliary channel functions

3. **System Status**
   - Confirm "Calibrated" status on display
   - Check for any error messages
   - Verify all sensors are operational

### Initial Flight Test
1. **Ground Test**
   - Arm motors (props off)
   - Test motor response to controls
   - Verify failsafe operation

2. **Hover Test**
   - Install propellers
   - Perform brief hover test
   - Check stability and control response

3. **Full Flight Test**
   - Test all flight modes
   - Verify GPS functions (if applicable)
   - Check failsafe behavior

## Recalibration Schedule

### When to Recalibrate
- **After any hardware changes**
- **If flight behavior changes**
- **After crashes or hard landings**
- **When moving to new geographic location**
- **If environmental conditions change significantly**

### Periodic Maintenance
- **Monthly**: Check calibration status
- **Before important flights**: Verify all systems
- **After transport**: Quick calibration check
- **Seasonal**: Full recalibration cycle

## Troubleshooting Common Issues

### Calibration Won't Start
- Check serial communication
- Verify sensor connections
- Ensure stable power supply

### Calibration Fails Midway
- Check for vibrations or movement
- Verify environmental conditions
- Restart and try again

### Poor Flight Performance After Calibration
- Verify calibration values are reasonable
- Check sensor mounting and orientation
- Consider environmental factors

### Values Keep Changing
- Check for loose connections
- Verify stable mounting
- Consider temperature effects

## Advanced Calibration

### Multi-Point Accelerometer Calibration
For improved accuracy, perform 6-point calibration:
1. Level position (Z-up)
2. Inverted position (Z-down)
3. Right side (Y-up)
4. Left side (Y-down)
5. Nose up (X-up)
6. Nose down (X-down)

### Temperature Compensation
For precision applications:
- Record calibration values at different temperatures
- Implement temperature compensation algorithms
- Consider thermal stabilization time

### Field Calibration
Quick field recalibration procedures:
- Simplified gyro calibration (30 seconds)
- Quick magnetometer check
- Barometer reference update
