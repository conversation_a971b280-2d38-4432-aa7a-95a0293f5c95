/*
 * Motor Control for ESP32 Quadcopter Flight Controller
 * 
 * This module handles ESC control and motor mixing for quadcopter flight
 * Supports X-configuration quadcopter layout
 */

#ifndef MOTOR_CONTROL_H
#define MOTOR_CONTROL_H

#include "config.h"

// Forward declarations
void disarmMotors();

// Motor mixing coefficients for X-configuration
#define MOTOR_FR_ROLL_COEFF    -1  // Front Right: negative roll
#define MOTOR_FR_PITCH_COEFF   -1  // Front Right: negative pitch
#define MOTOR_FR_YAW_COEFF     -1  // Front Right: negative yaw

#define MOTOR_FL_ROLL_COEFF     1  // Front Left: positive roll
#define MOTOR_FL_PITCH_COEFF   -1  // Front Left: negative pitch
#define MOTOR_FL_YAW_COEFF      1  // Front Left: positive yaw

#define MOTOR_RR_ROLL_COEFF    -1  // Rear Right: negative roll
#define MOTOR_RR_PITCH_COEFF    1  // Rear Right: positive pitch
#define MOTOR_RR_YAW_COEFF      1  // Rear Right: positive yaw

#define MOTOR_RL_ROLL_COEFF     1  // Rear Left: positive roll
#define MOTOR_RL_PITCH_COEFF    1  // Rear Left: positive pitch
#define MOTOR_RL_YAW_COEFF     -1  // Rear Left: negative yaw

// PWM channels for ESP32 LEDC
#define MOTOR_FR_CHANNEL    0
#define MOTOR_FL_CHANNEL    1
#define MOTOR_RR_CHANNEL    2
#define MOTOR_RL_CHANNEL    3

// PWM parameters
#define PWM_FREQUENCY       500    // 500Hz for ESCs
#define PWM_RESOLUTION      12     // 12-bit resolution (0-4095)
#define PWM_MAX_VALUE       4095   // Maximum PWM value

// Motor data
extern MotorData motorOutputs;
extern bool motorsArmed;

// Function declarations
bool initializeMotorControl();
void updateMotorOutputs();
void mixMotors();
void armMotors();
void disarmMotors();
bool checkArmingConditions();
void setMotorSpeed(int channel, int speed);
void setAllMotors(int speed);
void testMotors();

// Safety functions
bool isThrottleLow();
bool isQuadcopterLevel();
void emergencyStop();

// Utility functions
int mapPWMToESC(int pwmValue);
int constrainMotor(int value);

#endif // MOTOR_CONTROL_H
