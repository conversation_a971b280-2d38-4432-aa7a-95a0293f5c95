/*
 * Display management for ESP32 Quadcopter Flight Controller
 * 
 * This module handles the 1.8" TFT SPI display for showing flight data
 */

#ifndef DISPLAY_H
#define DISPLAY_H

#include "config.h"
#include <Adafruit_GFX.h>
#include <Adafruit_ST7735.h>

// Display object
extern Adafruit_ST7735 tft;

// Display parameters
#define DISPLAY_WIDTH   128
#define DISPLAY_HEIGHT  160
#define TEXT_SIZE       1
#define LINE_HEIGHT     8

// Colors
#define COLOR_BLACK     ST77XX_BLACK
#define COLOR_WHITE     ST77XX_WHITE
#define COLOR_RED       ST77XX_RED
#define COLOR_GREEN     ST77XX_GREEN
#define COLOR_BLUE      ST77XX_BLUE
#define COLOR_YELLOW    ST77XX_YELLOW
#define COLOR_CYAN      ST77XX_CYAN
#define COLOR_MAGENTA   ST77XX_MAGENTA

// Display pages
enum DisplayPage {
  PAGE_FLIGHT_DATA = 0,
  PAGE_GPS_DATA = 1,
  PAGE_SYSTEM_STATUS = 2,
  PAGE_RC_DATA = 3,
  PAGE_COUNT = 4
};

extern DisplayPage currentPage;

// Function declarations
bool initializeDisplay();
void updateDisplay();
void clearDisplay();
void displayFlightData();
void displayGPSData();
void displaySystemStatus();
void displayRCData();
void displayFlightMode(FlightMode mode);
void displayArmedStatus(bool armed);
void displayBatteryStatus(float voltage);
void displayGPSStatus();
void nextDisplayPage();
void previousDisplayPage();

// Drawing functions
void drawProgressBar(int x, int y, int width, int height, float percentage, uint16_t color);
void drawCenterText(const char* text, int y, uint16_t color);
void drawRightAlignedText(const char* text, int y, uint16_t color);

// Utility functions
void formatFloat(char* buffer, float value, int decimals);
void formatTime(char* buffer, unsigned long milliseconds);

#endif // DISPLAY_H
