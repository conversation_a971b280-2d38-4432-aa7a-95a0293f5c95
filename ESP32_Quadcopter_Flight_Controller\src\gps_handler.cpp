/*
 * GPS Handler implementation
 */

#include "gps_handler.h"

// GPS objects
TinyGPSPlus gps;
HardwareSerial SerialGPS(2);

// GPS data
GPSData gpsData;

// Home position
double homeLatitude = 0;
double homeLongitude = 0;
float homeAltitude = 0;
bool homePositionSet = false;

bool initializeGPS() {
  // Initialize GPS serial communication
  SerialGPS.begin(GPS_BAUD_RATE, SERIAL_8N1, GPS_RX_PIN, GPS_TX_PIN);
  
  // Initialize GPS data structure
  gpsData.latitude = 0;
  gpsData.longitude = 0;
  gpsData.altitude = 0;
  gpsData.speed = 0;
  gpsData.course = 0;
  gpsData.satellites = 0;
  gpsData.hdop = 99.99;
  gpsData.fix = false;
  gpsData.lastUpdate = millis();
  
  Serial.println("GPS initialized - Waiting for fix...");
  return true;
}

void updateGPS() {
  // Read available GPS data
  while (SerialGPS.available() > 0) {
    if (gps.encode(SerialGPS.read())) {
      processGPSData();
    }
  }
  
  // Check for GPS timeout
  if (millis() - gpsData.lastUpdate > 5000) {
    gpsData.fix = false;
  }
}

void processGPSData() {
  // Update GPS data if valid
  if (gps.location.isValid()) {
    gpsData.latitude = gps.location.lat();
    gpsData.longitude = gps.location.lng();
    gpsData.lastUpdate = millis();
    gpsData.fix = true;
  }
  
  if (gps.altitude.isValid()) {
    gpsData.altitude = gps.altitude.meters();
  }
  
  if (gps.speed.isValid()) {
    gpsData.speed = gps.speed.mps(); // meters per second
  }
  
  if (gps.course.isValid()) {
    gpsData.course = gps.course.deg();
  }
  
  if (gps.satellites.isValid()) {
    gpsData.satellites = gps.satellites.value();
  }
  
  if (gps.hdop.isValid()) {
    gpsData.hdop = gps.hdop.hdop();
  }
  
  // Update system state
  extern SystemState systemState;
  systemState.gpsData = gpsData;
}

bool isGPSFixValid() {
  return (gpsData.fix && 
          gpsData.satellites >= GPS_MIN_SATELLITES && 
          gpsData.hdop <= GPS_MAX_HDOP);
}

void setHomePosition() {
  if (isGPSFixValid()) {
    homeLatitude = gpsData.latitude;
    homeLongitude = gpsData.longitude;
    homeAltitude = gpsData.altitude;
    homePositionSet = true;
    
    Serial.println("Home position set:");
    Serial.print("Lat: "); Serial.println(homeLatitude, 6);
    Serial.print("Lon: "); Serial.println(homeLongitude, 6);
    Serial.print("Alt: "); Serial.println(homeAltitude, 1);
  } else {
    Serial.println("Cannot set home position - GPS fix not valid");
  }
}

void clearHomePosition() {
  homePositionSet = false;
  homeLatitude = 0;
  homeLongitude = 0;
  homeAltitude = 0;
  Serial.println("Home position cleared");
}

float calculateDistanceToHome() {
  if (!homePositionSet || !isGPSFixValid()) {
    return 0;
  }
  
  return calculateDistance(gpsData.latitude, gpsData.longitude, 
                          homeLatitude, homeLongitude);
}

float calculateBearingToHome() {
  if (!homePositionSet || !isGPSFixValid()) {
    return 0;
  }
  
  return calculateBearing(gpsData.latitude, gpsData.longitude, 
                         homeLatitude, homeLongitude);
}

float calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  // Haversine formula for calculating distance between two GPS coordinates
  const float R = 6371000; // Earth's radius in meters
  
  float dLat = degreesToRadians(lat2 - lat1);
  float dLon = degreesToRadians(lon2 - lon1);
  
  float a = sin(dLat/2) * sin(dLat/2) +
            cos(degreesToRadians(lat1)) * cos(degreesToRadians(lat2)) *
            sin(dLon/2) * sin(dLon/2);
  
  float c = 2 * atan2(sqrt(a), sqrt(1-a));
  
  return R * c; // Distance in meters
}

float calculateBearing(double lat1, double lon1, double lat2, double lon2) {
  // Calculate bearing from point 1 to point 2
  float dLon = degreesToRadians(lon2 - lon1);
  
  float lat1Rad = degreesToRadians(lat1);
  float lat2Rad = degreesToRadians(lat2);
  
  float y = sin(dLon) * cos(lat2Rad);
  float x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(dLon);
  
  float bearing = radiansToDegrees(atan2(y, x));
  
  // Normalize to 0-360 degrees
  if (bearing < 0) {
    bearing += 360;
  }
  
  return bearing;
}

void printGPSStatus() {
  Serial.print("GPS: ");
  if (gpsData.fix) {
    Serial.print("FIX ");
    Serial.print("Sats: "); Serial.print(gpsData.satellites);
    Serial.print(" HDOP: "); Serial.print(gpsData.hdop, 1);
    Serial.print(" Lat: "); Serial.print(gpsData.latitude, 6);
    Serial.print(" Lon: "); Serial.print(gpsData.longitude, 6);
    Serial.print(" Alt: "); Serial.print(gpsData.altitude, 1);
    Serial.print("m Speed: "); Serial.print(gpsData.speed, 1);
    Serial.println("m/s");
  } else {
    Serial.println("NO FIX");
  }
}

bool hasGoodGPSFix() {
  return isGPSFixValid();
}

void waitForGPSFix() {
  Serial.println("Waiting for GPS fix...");
  
  while (!hasGoodGPSFix()) {
    updateGPS();
    
    if (millis() % 1000 == 0) {
      Serial.print("GPS Status: Sats=");
      Serial.print(gpsData.satellites);
      Serial.print(" HDOP=");
      Serial.print(gpsData.hdop, 1);
      Serial.print(" Fix=");
      Serial.println(gpsData.fix ? "YES" : "NO");
    }
    
    delay(100);
  }
  
  Serial.println("GPS fix acquired!");
  printGPSStatus();
}

float degreesToRadians(float degrees) {
  return degrees * PI / 180.0;
}

float radiansToDegrees(float radians) {
  return radians * 180.0 / PI;
}
