# Sensor Calibration and GPS Integration Procedures

Accurate sensor data is fundamental for stable flight and reliable navigation in a quadcopter. This section details the calibration procedures for the GY-86 10DOF module (MPU6050, HMC5883L, MS5611) and the integration method for the NEO-6M GPS module, including its application for position hold.

## 1. GY-86 10DOF Module Calibration Procedures

Calibration is essential to compensate for manufacturing imperfections, sensor biases, and environmental influences. Each sensor within the GY-86 module requires a specific calibration process.

### 1.1. MPU6050 Accelerometer Calibration

The accelerometer measures linear acceleration along the X, Y, and Z axes. Calibration involves determining and compensating for static biases (offsets) that cause the sensor to report non-zero values even when stationary. The goal is to ensure that when the accelerometer is perfectly level, its X and Y axes read close to 0g, and its Z axis reads close to +1g (due to gravity).

**Procedure:**

1.  **Mounting:** Securely mount the GY-86 module to the drone frame in its final orientation. This is crucial because the calibration values are specific to the sensor's orientation relative to gravity.
2.  **Software Setup:** Load a dedicated accelerometer calibration sketch onto the ESP32. This sketch should read raw accelerometer values and send them to the Serial Monitor.
3.  **Data Collection:**
    *   Place the drone on a perfectly level surface. Ensure it is stable and not moving.
    *   Record a significant number of readings (e.g., 1000-2000 samples) for each axis (Ax, Ay, Az) while the drone is stationary and level. Calculate the average of these readings.
    *   Rotate the drone to six different orientations (each axis pointing up and down) and repeat the data collection and averaging for each orientation. The six orientations are: X-axis up, X-axis down, Y-axis up, Y-axis down, Z-axis up, Z-axis down.
        *   **X-axis up:** Place the drone so its X-axis points directly upwards.
        *   **X-axis down:** Place the drone so its X-axis points directly downwards.
        *   **Y-axis up:** Place the drone so its Y-axis points directly upwards.
        *   **Y-axis down:** Place the drone so its Y-axis points directly downwards.
        *   **Z-axis up:** Place the drone so its Z-axis points directly upwards (normal flying position).
        *   **Z-axis down:** Place the drone so its Z-axis points directly downwards.
4.  **Offset Calculation:**
    *   For each axis (X, Y, Z), the offset is calculated by averaging the readings from its positive and negative orientations and subtracting the expected gravitational value (0g for X and Y, +1g or -1g for Z depending on orientation).
    *   For example, `offset_x = (Ax_positive_g + Ax_negative_g) / 2`.
    *   The `+1g` and `-1g` values for the Z-axis (and 0g for X and Y) are the ideal readings when the sensor is perfectly aligned with gravity. The actual raw readings will deviate from these ideals.
    *   A common simplified approach for initial calibration is to take multiple readings with the drone perfectly level (Z-axis up) and calculate the average for each axis. These averages become your offsets. For example, if `Ax_avg = 150`, `Ay_avg = -70`, `Az_avg = 16384` (assuming 1g = 16384 for a 2g range), then `offset_x = 150`, `offset_y = -70`, `offset_z = 16384 - 16384 = 0` (if your raw Z is already 1g). More robust methods involve the 6-point calibration.
5.  **Apply Offsets:** Subtract the calculated offsets from all subsequent raw accelerometer readings in your flight control code. This will shift the readings so that when the sensor is stationary and level, X and Y are near zero, and Z is near the expected 1g.

**Example Code Snippet (Conceptual):**

```cpp
// In setup() or a dedicated calibration function
void calibrateAccelerometer() {
  long sumAx = 0, sumAy = 0, sumAz = 0;
  int numReadings = 1000;

  Serial.println("Calibrating Accelerometer... Keep drone still and level.");
  for (int i = 0; i < numReadings; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    sumAx += a.acceleration.x;
    sumAy += a.acceleration.y;
    sumAz += a.acceleration.z;
    delay(5); // Small delay between readings
  }

  accelOffsetX = sumAx / numReadings;
  accelOffsetY = sumAy / numReadings;
  // For Z-axis, if 1g is 16384 (for 2g range), and your average Az is 16400, offset is 16400 - 16384 = 16
  // This assumes the drone is level (Z-axis up) during this simple calibration.
  accelOffsetZ = (sumAz / numReadings) - 16384; // Adjust 16384 based on your MPU6050 sensitivity setting

  Serial.print("Accel Offsets: X="); Serial.print(accelOffsetX);
  Serial.print(", Y="); Serial.print(accelOffsetY);
  Serial.print(", Z="); Serial.println(accelOffsetZ);
}

// In readIMU() function:
// float calibratedAx = rawAx - accelOffsetX;
// float calibratedAy = rawAy - accelOffsetY;
// float calibratedAz = rawAz - accelOffsetZ;
```

### 1.2. MPU6050 Gyroscope Calibration

The gyroscope measures angular velocity. When the drone is stationary, the gyroscope should ideally report zero angular velocity. Calibration involves determining the static bias (drift) of the gyroscope.

**Procedure:**

1.  **Mounting:** Ensure the GY-86 module is securely mounted.
2.  **Software Setup:** Load a dedicated gyroscope calibration sketch. This sketch should read raw gyroscope values and send them to the Serial Monitor.
3.  **Data Collection:**
    *   Place the drone on a perfectly stable and level surface. Ensure it is absolutely still and not vibrating.
    *   Record a large number of readings (e.g., 2000-5000 samples) for each axis (Gx, Gy, Gz) while the drone is stationary.
    *   Calculate the average of these readings for each axis. These averages represent the gyroscope's static bias or offset.
4.  **Apply Offsets:** Subtract the calculated offsets from all subsequent raw gyroscope readings in your flight control code. This will zero out the gyroscope readings when the drone is stationary.

**Example Code Snippet (Conceptual):**

```cpp
// In setup() or a dedicated calibration function
void calibrateGyroscope() {
  long sumGx = 0, sumGy = 0, sumGz = 0;
  int numReadings = 2000;

  Serial.println("Calibrating Gyroscope... Keep drone absolutely still.");
  for (int i = 0; i < numReadings; i++) {
    sensors_event_t a, g, temp;
    mpu.getEvent(&a, &g, &temp);
    sumGx += g.gyro.x;
    sumGy += g.gyro.y;
    sumGz += g.gyro.z;
    delay(2); // Small delay
  }

  gyroOffsetX = sumGx / numReadings;
  gyroOffsetY = sumGy / numReadings;
  gyroOffsetZ = sumGz / numReadings;

  Serial.print("Gyro Offsets: X="); Serial.print(gyroOffsetX);
  Serial.print(", Y="); Serial.print(gyroOffsetY);
  Serial.print(", Z="); Serial.println(gyroOffsetZ);
}

// In readIMU() function:
// float calibratedGx = rawGx - gyroOffsetX;
// float calibratedGy = rawGy - gyroOffsetY;
// float calibratedGz = rawGz - gyroOffsetZ;
```

### 1.3. HMC5883L Magnetometer Calibration

The magnetometer measures the Earth's magnetic field. Calibration is necessary to compensate for hard-iron (fixed magnetic fields from drone components) and soft-iron (distortion of the Earth's magnetic field by drone components) distortions. This typically involves moving the sensor in a figure-eight pattern to map the magnetic field in all directions.

**Procedure:**

1.  **Software Setup:** Load a magnetometer calibration sketch onto the ESP32. This sketch should read raw magnetometer values and send them to the Serial Monitor. It should also track the minimum and maximum values observed for each axis.
2.  **Data Collection (Figure-Eight Motion):**
    *   Hold the drone in your hand, away from any large metal objects or strong magnetic fields (e.g., speakers, power supplies).
    *   Slowly rotate the drone through all possible orientations, performing a 


figure-eight motion or rotating it around all three axes (roll, pitch, yaw) for at least 30-60 seconds. Ensure that the sensor experiences the full range of magnetic field variations.
    *   The calibration sketch should continuously record the minimum and maximum raw readings for X, Y, and Z axes.
3.  **Offset and Scale Factor Calculation:**
    *   **Hard-Iron Offsets:** Calculate the offset for each axis as `offset = (max_value + min_value) / 2`. These offsets represent the center of the magnetic sphere/ellipsoid.
    *   **Soft-Iron Scale Factors (Optional but Recommended):** For more accurate compensation, soft-iron distortions can be addressed. This involves calculating scale factors to transform the magnetic ellipsoid into a sphere. This is more complex and often requires a least-squares fitting algorithm or a dedicated calibration library. For a basic drone, hard-iron compensation might be sufficient.
4.  **Apply Offsets:** Subtract the calculated hard-iron offsets from all subsequent raw magnetometer readings in your flight control code.

**Example Code Snippet (Conceptual for Hard-Iron):**

```cpp
// In setup() or a dedicated calibration function
void calibrateMagnetometer() {
  Serial.println("Calibrating Magnetometer... Rotate drone in figure-eight pattern.");
  long magMinX = 0, magMaxX = 0;
  long magMinY = 0, magMaxY = 0;
  long magMinZ = 0, magMaxZ = 0;

  // Loop for a duration (e.g., 30 seconds) to collect data
  unsigned long startTime = millis();
  while (millis() - startTime < 30000) { // Collect data for 30 seconds
    sensors_event_t mag_event;
    mag.getEvent(&mag_event);

    // Update min/max values
    if (mag_event.magnetic.x < magMinX) magMinX = mag_event.magnetic.x;
    if (mag_event.magnetic.x > magMaxX) magMaxX = mag_event.magnetic.x;
    if (mag_event.magnetic.y < magMinY) magMinY = mag_event.magnetic.y;
    if (mag_event.magnetic.y > magMaxY) magMaxY = mag_event.magnetic.y;
    if (mag_event.magnetic.z < magMinZ) magMinZ = mag_event.magnetic.z;
    if (mag_event.magnetic.z > magMaxZ) magMaxZ = mag_event.magnetic.z;

    delay(10); // Small delay
  }

  magOffsetX = (magMaxX + magMinX) / 2;
  magOffsetY = (magMaxY + magMinY) / 2;
  magOffsetZ = (magMaxZ + magMinZ) / 2;

  Serial.print("Mag Offsets: X="); Serial.print(magOffsetX);
  Serial.print(", Y="); Serial.print(magOffsetY);
  Serial.print(", Z="); Serial.println(magOffsetZ);
}

// In readIMU() function:
// float calibratedMagX = rawMagX - magOffsetX;
// float calibratedMagY = rawMagY - magOffsetY;
// float calibratedMagZ = rawMagZ - magOffsetZ;
```

### 1.4. MS5611 Barometer Calibration

The MS5611 barometer provides pressure and temperature readings. While the sensor itself is factory calibrated, its readings are affected by ambient temperature and the drone's altitude. The primary calibration for a barometer in a drone context is to establish a reference pressure at ground level and to compensate for temperature drift.

**Procedure:**

1.  **Initial Reading:** Take an initial pressure reading when the drone is on the ground, before flight. This will serve as your reference pressure for altitude calculations.
2.  **Temperature Compensation:** The MS5611 provides internal temperature compensation, but external temperature changes can still affect accuracy. Ensure your code correctly uses the temperature readings provided by the MS5611 to calculate compensated pressure.
3.  **Altitude Calculation:** Altitude is calculated using the barometric formula, which relates pressure to altitude. The formula requires a reference pressure (pressure at sea level or ground level) and the current pressure.

    The simplified formula for altitude is:
    `Altitude (meters) = 44330 * (1 - (Current_Pressure / Reference_Pressure)^(1/5.255))`

    Where:
    *   `Current_Pressure` is the pressure reading from the MS5611.
    *   `Reference_Pressure` is the pressure at your starting altitude (e.g., ground level before takeoff).

**Example Code Snippet (Conceptual):**

```cpp
// Global variable
float groundPressure = 0;

void setupBarometer() {
  // Initialize MS5611
  // ...
  // Take initial ground pressure reading after a short delay for stabilization
  delay(1000);
  groundPressure = ms5611.readPressure();
  Serial.print("Ground Pressure: "); Serial.println(groundPressure);
}

void readBarometer() {
  // Read raw pressure and temperature
  pressure = ms5611.readPressure();
  temperature = ms5611.readTemperature();

  // Calculate altitude using the barometric formula
  // Ensure groundPressure is set before calculating altitude
  if (groundPressure > 0) {
    altitude = 44330.0 * (1.0 - pow(pressure / groundPressure, 1 / 5.255));
  }
}
```

## 2. GPS Integration Method for Position Hold

Integrating the NEO-6M GPS module allows the drone to maintain a stable position, a crucial feature for autonomous flight. Position hold works by using GPS data to detect drift and then applying corrections via the PID controller to bring the drone back to its target coordinates.

### 2.1. GPS Data Acquisition and Parsing

As outlined in the code structure, the `TinyGPS++` library simplifies parsing NMEA sentences from the NEO-6M module. The ESP32's `HardwareSerial` (UART2) will be used for communication.

**Key Steps:**

1.  **Initialize UART:** Configure `HardwareSerial` for the GPS module with the correct baud rate (typically 9600 for NEO-6M).
2.  **Read and Encode Data:** Continuously read bytes from the GPS serial port and feed them to `gps.encode()`. This function processes the NMEA sentences and updates the internal `TinyGPS++` object with the latest GPS data.
3.  **Validate Data:** Before using GPS data, always check its validity using functions like `gps.location.isValid()`, `gps.altitude.isValid()`, `gps.satellites.isValid()`, etc. This ensures you are using reliable data.
4.  **Extract Data:** Once valid, extract latitude, longitude, altitude, number of satellites, and HDOP (Horizontal Dilution of Precision) from the `TinyGPS++` object.

### 2.2. Position Hold Algorithm

Position hold typically involves two nested PID loops: one for horizontal position (X and Y axes) and another for altitude.

**Horizontal Position Hold (X-Y Plane):**

1.  **Set Target:** When position hold is activated (e.g., via an RC switch), the drone records its current GPS latitude and longitude as the target coordinates.
2.  **Calculate Error:** Continuously calculate the distance and bearing from the current GPS position to the target GPS position. This can be done using Haversine formula or simpler approximations for short distances.
    *   `distance_error_north = current_latitude - target_latitude`
    *   `distance_error_east = current_longitude - target_longitude`
    *   Convert these latitude/longitude differences into meters using appropriate conversion factors (e.g., 1 degree latitude is approx 111,320 meters; 1 degree longitude varies with latitude).
3.  **PID Control:** Apply separate PID controllers for the North-South (Pitch) and East-West (Roll) axes.
    *   **Pitch PID:** Takes `distance_error_north` as input and outputs a pitch correction (desired pitch angle).
    *   **Roll PID:** Takes `distance_error_east` as input and outputs a roll correction (desired roll angle).
4.  **Feed to Attitude PID:** The outputs from the position PID controllers (desired roll and pitch angles) are then fed as inputs to the main attitude PID controllers (which use IMU data to stabilize the drone).

**Altitude Hold (Z-Axis):**

1.  **Set Target:** When altitude hold is activated, the drone records its current altitude (from barometer or GPS altitude if available and reliable) as the target altitude.
2.  **Calculate Error:** Continuously calculate the difference between the current altitude and the target altitude: `altitude_error = target_altitude - current_altitude`.
3.  **PID Control:** Apply a PID controller for altitude.
    *   **Altitude PID:** Takes `altitude_error` as input and outputs a throttle correction. If the drone is too low, it increases throttle; if too high, it decreases throttle.
4.  **Feed to Throttle:** The output from the altitude PID controller is added to the base throttle value (e.g., from RC input) before being sent to the motors.

### 2.3. Considerations for GPS Integration

*   **GPS Lock:** Ensure the GPS module has a sufficient number of satellites (e.g., 6 or more) and a good HDOP value (e.g., less than 2.0) before attempting position hold. Display GPS status on the TFT screen.
*   **Interference:** Keep the GPS antenna away from noisy components (ESCs, motors, power lines) to minimize interference.
*   **Update Rate:** The NEO-6M's 1Hz update rate can be a limitation for very precise position hold. For faster response, a 5Hz update rate can be configured in the GPS module (if supported by your specific module and firmware, typically via u-center software).
*   **Sensor Fusion:** For more robust altitude hold, consider fusing barometer altitude with GPS altitude (if available) using a Kalman filter, as barometric altitude can drift with weather changes, and GPS altitude can be less accurate than horizontal position.
*   **Drift:** Even with position hold, some drift is inevitable due to GPS inaccuracies and environmental factors (wind). The PID gains for position hold will need careful tuning.

By following these calibration and integration procedures, you will ensure that your drone's sensors provide accurate data, and its GPS system can effectively contribute to stable flight and autonomous capabilities. The next phase will focus on configuring RC channel mapping and tuning PID control parameters.

