# 1 "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp9d1n428f"
#include <Arduino.h>
# 1 "C:/Users/<USER>/Desktop/quadcopternew/ESP32_Quadcopter_Flight_Controller/src/ESP32_Quadcopter_Flight_Controller.ino"
# 19 "C:/Users/<USER>/Desktop/quadcopternew/ESP32_Quadcopter_Flight_Controller/src/ESP32_Quadcopter_Flight_Controller.ino"
#include "config.h"
#include "sensors.h"
#include "pid_controller.h"
#include "rc_input.h"
#include "motor_control.h"
#include "gps_handler.h"
#include "display.h"
#include "failsafe.h"


SystemState systemState;
FlightMode currentFlightMode = STABILIZE_MODE;
bool motorsArmed = false;
bool systemInitialized = false;


extern RCData rcData;


const char* getFlightModeName(FlightMode mode);


unsigned long lastLoopTime = 0;
unsigned long lastDisplayUpdate = 0;
unsigned long lastGPSUpdate = 0;
unsigned long lastTelemetryUpdate = 0;
void setup();
void loop();
bool initializeSystem();
void updateFlightMode();
void sendTelemetry();
#line 46 "C:/Users/<USER>/Desktop/quadcopternew/ESP32_Quadcopter_Flight_Controller/src/ESP32_Quadcopter_Flight_Controller.ino"
void setup() {
  Serial.begin(115200);
  Serial.println("ESP32 Quadcopter Flight Controller Starting...");


  if (!initializeSystem()) {
    Serial.println("CRITICAL ERROR: System initialization failed!");
    while(1) {

      digitalWrite(2, HIGH);
      delay(100);
      digitalWrite(2, LOW);
      delay(100);
    }
  }

  Serial.println("System initialization complete!");
  Serial.println("Ready for flight operations.");
  systemInitialized = true;
}

void loop() {
  unsigned long currentTime = micros();
  float deltaTime = (currentTime - lastLoopTime) / 1000000.0f;
  lastLoopTime = currentTime;


  if (systemInitialized) {

    updateSensors();


    updateRCInputs();


    if (millis() - lastGPSUpdate > GPS_UPDATE_INTERVAL) {
      updateGPS();
      lastGPSUpdate = millis();
    }


    checkFailsafe();


    updateFlightMode();


    if (motorsArmed && !systemState.failsafeActive) {
      calculatePIDOutputs(deltaTime);


      updateMotorOutputs();
    } else {

      disarmMotors();
    }


    if (millis() - lastDisplayUpdate > DISPLAY_UPDATE_INTERVAL) {
      updateDisplay();
      lastDisplayUpdate = millis();
    }


    if (millis() - lastTelemetryUpdate > TELEMETRY_UPDATE_INTERVAL) {
      sendTelemetry();
      lastTelemetryUpdate = millis();
    }
  }


  delayMicroseconds(100);
}

bool initializeSystem() {
  Serial.println("Initializing system components...");


  pinMode(2, OUTPUT);
  digitalWrite(2, LOW);


  Wire.begin(I2C_SDA_PIN, I2C_SCL_PIN);
  Wire.setClock(400000);


  Serial.println("Initializing sensors...");
  if (!initializeSensors()) {
    Serial.println("ERROR: Sensor initialization failed!");
    return false;
  }


  Serial.println("Initializing RC input...");
  if (!initializeRCInput()) {
    Serial.println("ERROR: RC input initialization failed!");
    return false;
  }


  Serial.println("Initializing motor control...");
  if (!initializeMotorControl()) {
    Serial.println("ERROR: Motor control initialization failed!");
    return false;
  }


  Serial.println("Initializing GPS...");
  if (!initializeGPS()) {
    Serial.println("WARNING: GPS initialization failed - continuing without GPS");
  }


  Serial.println("Initializing display...");
  if (!initializeDisplay()) {
    Serial.println("WARNING: Display initialization failed - continuing without display");
  }


  Serial.println("Initializing PID controllers...");
  initializePIDControllers();


  Serial.println("Initializing failsafe system...");
  initializeFailsafe();


  Serial.println("Checking sensor calibration...");
  if (!checkSensorCalibration()) {
    Serial.println("WARNING: Sensors may need calibration!");
  }

  Serial.println("System initialization completed successfully!");
  return true;
}

void updateFlightMode() {
  FlightMode newMode = determineFlightModeFromRC();

  if (newMode != currentFlightMode) {
    Serial.print("Flight mode changed from ");
    Serial.print(getFlightModeName(currentFlightMode));
    Serial.print(" to ");
    Serial.println(getFlightModeName(newMode));

    currentFlightMode = newMode;


    resetPIDIntegrators();


    displayFlightMode(currentFlightMode);
  }
}

void sendTelemetry() {

  Serial.print("TELEM,");
  Serial.print(millis());
  Serial.print(",");
  Serial.print(systemState.roll, 2);
  Serial.print(",");
  Serial.print(systemState.pitch, 2);
  Serial.print(",");
  Serial.print(systemState.yaw, 2);
  Serial.print(",");
  Serial.print(systemState.altitude, 2);
  Serial.print(",");
  Serial.print(systemState.gpsData.latitude, 6);
  Serial.print(",");
  Serial.print(systemState.gpsData.longitude, 6);
  Serial.print(",");
  Serial.print(systemState.gpsData.satellites);
  Serial.print(",");
  Serial.print(systemState.batteryVoltage, 2);
  Serial.print(",");
  Serial.print(getFlightModeName(currentFlightMode));
  Serial.print(",");
  Serial.print(motorsArmed ? "ARMED" : "DISARMED");
  Serial.print(",");
  Serial.println(systemState.failsafeActive ? "FAILSAFE" : "NORMAL");
}

const char* getFlightModeName(FlightMode mode) {
  switch(mode) {
    case ACRO_MODE: return "ACRO";
    case STABILIZE_MODE: return "STABILIZE";
    case ALTITUDE_HOLD_MODE: return "ALT_HOLD";
    case POSITION_HOLD_MODE: return "POS_HOLD";
    case RETURN_TO_LAUNCH_MODE: return "RTL";
    case LAND_MODE: return "LAND";
    default: return "UNKNOWN";
  }
}