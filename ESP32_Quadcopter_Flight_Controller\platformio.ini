; PlatformIO Project Configuration File for ESP32 Quadcopter Flight Controller
;
; This file provides an alternative to Arduino IDE for building and uploading
; the quadcopter firmware using PlatformIO.
;
; Installation:
; 1. Install PlatformIO Core or PlatformIO IDE
; 2. Open this project folder in PlatformIO
; 3. Build and upload using PlatformIO commands

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Serial Monitor Configuration
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Build Configuration
build_flags = 
    -DCORE_DEBUG_LEVEL=3
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue

; Library Dependencies
lib_deps = 
    adafruit/Adafruit GFX Library@^1.11.3
    adafruit/Adafruit ST7735 and ST7789 Library@^1.9.3
    adafruit/Adafruit MPU6050@^2.2.4
    adafruit/Adafruit HMC5883 Unified @ ^1.2.3
    robtillaart/MS5611 @ ^0.4.1
    adafruit/Adafruit Unified Sensor@^1.1.7
    mikalhart/TinyGPSPlus@^1.0.3

; Upload Configuration
upload_speed = 921600
upload_port = COM5

; Advanced Build Options
build_type = release
build_unflags = -Os


; Board Configuration
board_build.mcu = esp32
board_build.f_cpu = 240000000L
board_build.f_flash = 80000000L
board_build.flash_mode = qio
board_build.partitions = default.csv
